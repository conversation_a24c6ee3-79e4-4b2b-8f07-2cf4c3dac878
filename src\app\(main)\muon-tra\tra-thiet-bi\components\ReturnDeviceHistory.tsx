"use client";

import { TablePageLayout } from "@/components/common";
import {
  FilterConfig,
  ActionType,
  ITableRef,
} from "@/components/common/TablePageLayout/type";
import { IReturnDeviceList } from "../returnDevice.model";
import { useRef, useMemo, useState } from "react";
import { ColumnDef } from "@tanstack/react-table";
import { formatDayjsWithType, formatNumber } from "@/utils/format.utils";
import { RETURN_DEVICE_HISTORY, TEACHER_COMBO } from "@/constant/api.const";
import { Button } from "@mui/material";

import ConfirmModal from "./ConfirmModal";
import FilterCustom from "./FilterCustom";

const ReturnDeviceHistory = () => {
  const tableRef = useRef<ITableRef>(null);
  const [selectedRows, setSelectedRows] = useState<IReturnDeviceList[]>([]);
  const [showConfirmModal, setShowConfirmModal] = useState(false);

  const customActions = useMemo(() => {
    return (
      <>
        <Button
          variant="contained"
          color="primary"
          onClick={() => setShowConfirmModal(true)}
          disabled={selectedRows.length === 0}
        >
          Bỏ ghi trả ({selectedRows.length})
        </Button>
      </>
    );
  }, [selectedRows]);

  const tableProps = useMemo(() => {
    return {
      columns: COLUMNS,
      onRowSelectionChange: (selectedRowsData: IReturnDeviceList[]) => {
        setSelectedRows(selectedRowsData);
      },
    };
  }, []);

  return (
    <>
      <TablePageLayout<IReturnDeviceList>
        ref={tableRef}
        apiUrl={RETURN_DEVICE_HISTORY}
        methodFetch="POST"
        tableProps={tableProps}
        actions={ACTIONS}
        filterConfig={FILTER_CONFIG}
        customActions={customActions}
        filterCustom={(props) => <FilterCustom props={props} />}
      />

      <ConfirmModal
        open={showConfirmModal}
        onClose={() => setShowConfirmModal(false)}
        onSuccess={() => {
          tableRef.current?.fetchCurrentData?.();
        }}
        type="cancel-returned"
        selectedRows={selectedRows}
      />
    </>
  );
};

const ACTIONS: ActionType[] = ["check"];

const FILTER_CONFIG: FilterConfig[] = [
  {
    key: "fromDate",
  },
  {
    key: "toDate",
  },
  {
    key: "teacherIds",
    type: "select",
    label: "Giáo viên",
    size: 2.4,
    isMulti: true,
    apiListUrl: TEACHER_COMBO,
    hasAllOption: true,
    isCollapse: true,
  },
  {
    key: "searchKey",
    type: "text",
    label: "Tìm kiếm",
    size: 2.4,
    isCollapse: true,
  },
];

const COLUMNS: ColumnDef<IReturnDeviceList>[] = [
  {
    id: "borrowReturnDate",
    header: "Ngày trả",
    accessorKey: "borrowReturnDate",
    cell: ({ row }) => formatDayjsWithType(row.original.borrowReturnDate),
  },
  {
    id: "teacherName",
    header: "Giáo viên",
    accessorKey: "teacherName",
  },
  {
    id: "deviceCode",
    header: "Mã thiết bị",
    accessorKey: "deviceCode",
  },
  {
    id: "deviceName",
    header: "Tên thiết bị",
    accessorKey: "deviceName",
  },
  {
    id: "deviceUnitName",
    header: "Đơn vị thiết bị",
    accessorKey: "deviceUnitName",
    size: 60,
  },
  {
    header: "Số lượng",
    size: 60,
    columns: [
      {
        id: "quantity",
        header: "Đã trả",
        accessorKey: "quantity",
        cell: ({ row }) => formatNumber(row.original.quantity),
        size: 60,
        meta: {
          align: "right",
        },
      },
      {
        id: "totalConsumed",
        header: "Tiêu hao",
        accessorKey: "totalConsumed",
        cell: ({ row }) => formatNumber(row.original.totalConsumed),
        size: 60,
        meta: {
          align: "right",
        },
      },
      {
        id: "totalBroken",
        header: "Hỏng",
        accessorKey: "totalBroken",
        cell: ({ row }) => formatNumber(row.original.totalBroken),
        size: 60,
        meta: {
          align: "right",
        },
      },
      {
        id: "totalLost",
        header: "Mất",
        accessorKey: "totalLost",
        cell: ({ row }) => formatNumber(row.original.totalLost),
        size: 60,
        meta: {
          align: "right",
        },
      },
    ],
  },
  {
    id: "roomName",
    header: "Kho/Phòng",
    accessorKey: "roomName",
  },
  {
    id: "notes",
    header: "Ghi chú",
    accessorKey: "notes",
  },
];

export default ReturnDeviceHistory;
