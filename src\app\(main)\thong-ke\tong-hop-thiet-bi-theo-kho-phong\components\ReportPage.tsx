"use client";

import { TablePageLayout } from "@/components/common";
import React from "react";
import {
  IReportDevice,
  StatisticDeviceStatusList,
} from "../reportDevice.model";
import { ColumnDef } from "@tanstack/react-table";
import { formatNumber } from "@/utils/format.utils";
import {
  ActionType,
  FilterConfig,
} from "@/components/common/TablePageLayout/type";
import { ApiConstant } from "@/constant";

const ReportPage = () => {
  return (
    <TablePageLayout<IReportDevice>
      methodFetch="POST"
      excelConfig={{
        tableName: "Báo cáo tổng hợp thiết bị theo kho/phòng",
        fileName: "bao_cao_tong_hop_thiet_bi_theo_kho_phong.xlsx",
      }}
      apiUrl="/api/statistic-report/device-by-room"
      tableProps={{
        columns: COLUMN,
      }}
      visibleCol={VISIBLE_COL}
      filterConfig={FILTER_CONFIG}
      actions={ACTIONS}
    />
  );
};
const ACTIONS: ActionType[] = ["exportExcel", "exportPDF"];

export default ReportPage;

const FILTER_CONFIG: FilterConfig[] = [
  {
    key: "roomIds",
    type: "select",
    isMulti: true,
    apiListUrl: ApiConstant.ROOM,
    label: "Kho/Phòng",
    hasAllOption: true,
  },
  {
    key: "schoolDeviceTypeIds",
    type: "select",
    isMulti: true,
    apiListUrl: ApiConstant.DEVICE_TYPE,
    label: "Loại thiết bị",
    hasAllOption: true,
  },
  {
    key: "schoolBudgetCategoryIds",
    type: "select",
    isMulti: true,
    apiListUrl: ApiConstant.SOURCE,
    label: "Nguồn cấp",
    hasAllOption: true,
  },
  {
    key: "schoolSubjectIds",
    type: "select",
    isMulti: true,
    apiListUrl: ApiConstant.SUBJECT,
    label: "Môn học",
    isCollapse: true,
    hasAllOption: true,
  },
  {
    key: "gradeCodes",
    type: "select",
    isMulti: true,
    apiListUrl: ApiConstant.GET_GRADE,
    label: "Khối/Lớp",
    isCollapse: true,
    hasAllOption: true,
  },
  {
    key: "statisticStatus",
    type: "select",
    isMulti: true,
    hasAllOption: true,
    options: StatisticDeviceStatusList,
    label: "Trạng thái thống kê",
    isCollapse: true,
  },
];

const COLUMN: ColumnDef<IReportDevice>[] = [
  {
    id: "roomName",
    header: "Kho/Phòng",
    accessorKey: "roomName",
    size: 150,
  },
  {
    id: "deviceName",
    header: "Tên thiết bị",
    accessorKey: "deviceName",
    size: 150,
  },
  {
    id: "schoolDeviceTypeName",
    header: "Loại thiết bị",
    accessorKey: "schoolDeviceTypeName",
    size: 120,
  },
  {
    id: "schoolSubjectName",
    header: "Môn học",
    accessorKey: "schoolSubjectName",
    size: 100,
  },
  {
    id: "gradeName",
    header: "Khối lớp",
    accessorKey: "gradeName",
    size: 100,
  },
  {
    id: "deviceUnitName",
    header: "Đơn vị tính",
    accessorKey: "deviceUnitName",
    size: 80,
  },
  {
    id: "total",
    header: "Số lượng",
    columns: [
      {
        id: "quantity",
        header: "Tổng",
        accessorKey: "quantity",
        accessorFn: (row) => formatNumber(row.quantity),
        size: 60,
        meta: {
          align: "right",
        },
      },
      {
        id: "totalBroken",
        header: "Hỏng",
        accessorKey: "totalBroken",
        accessorFn: (row) => formatNumber(row.totalBroken),
        size: 60,
        meta: {
          align: "right",
        },
      },
      {
        id: "totalLost",
        header: "Mất",
        accessorKey: "totalLost",
        accessorFn: (row) => formatNumber(row.totalLost),
        size: 60,
        meta: {
          align: "right",
        },
      },
      {
        id: "totalAvailable",
        header: "Còn SD",
        accessorKey: "totalAvailable",
        accessorFn: (row) => formatNumber(row.totalAvailable),
        size: 60,
        meta: {
          align: "right",
        },
      },
    ],
  },
];

const VISIBLE_COL = [
  { id: "roomName", name: "Kho/Phòng" },
  { id: "deviceName", name: "Tên thiết bị" },
  { id: "schoolDeviceTypeName", name: "Loại thiết bị" },
  { id: "schoolSubjectName", name: "Môn học" },
  { id: "gradeName", name: "Khối lớp" },
  { id: "deviceUnitName", name: "Đơn vị tính" },
  { id: "quantity", name: "SL Tổng" },
  { id: "totalBroken", name: "SL Hỏng" },
  { id: "totalLost", name: "SL Mất" },
  { id: "totalAvailable", name: "SL Còn SD" },
];
