export interface IReportDecreaseDevice {
  id: number;
  deviceDefinitionId: number;
  roomId: number;
  roomName: string;
  quantity: number;
  price: number;
  totalPrices: number;
  totalBroken: number;
  totalLost: number;
  totalAvailable: number;
  statisticCode: string;
  deviceCode: string;
  deviceName: string;
  deviceUnitId: number;
  deviceUnitName: string;
  schoolDeviceTypeId: number;
  schoolDeviceTypeName: string;
  deviceDTITypeId: number;
  deviceDTITypeName: string;
  schoolSubjectId: number;
  schoolSubjectName: string;
  gradeCodes: number[];
  gradeCode: string;
  gradeName: string;
  userType: string;
  userTypes: number[];
  documentDate: string;
}

export interface IReportDecreaseDeviceFilter {
  fromDate: string;
  toDate: string;
  roomIds: number[];
  schoolDeviceTypeIds: number[];
}
