import { memo } from "react";
import { Typography } from "@mui/material";
import AppDatePicker from "@/components/common/AppDatePicker";
import { useAppDispatch, useAppSelector } from "@/redux/hook";
import {
  selectHeaderDate,
  returnDeviceActions,
} from "../../../returnDevice.slice";

import dayjs from "dayjs";
import { DATE_TIME_YYYYescape } from "@/constant/app.const";

const DateHeaderWithRedux = memo(() => {
  const dispatch = useAppDispatch();
  const headerDate = useAppSelector(selectHeaderDate);

  const handleHeaderDateChange = (newValue: any) => {
    const formattedDate = newValue
      ? dayjs(newValue).format(DATE_TIME_YYYYescape)
      : null;

    if (formattedDate) {
      dispatch(returnDeviceActions.updateHeaderDateForAllRows(formattedDate));
    }
  };

  return (
    <>
      <Typography variant="body1" color="primary.contrastText">
        <PERSON><PERSON>y trả
      </Typography>
      <AppDatePicker
        value={headerDate ? dayjs(headerDate) : null}
        onChange={handleHeaderDateChange}
        slotProps={{
          textField: {
            size: "small",
            fullWidth: true,
            placeholder: "Ngày trả cho tất cả",
          },
        }}
        maxDate={dayjs()}
      />
    </>
  );
});

DateHeaderWithRedux.displayName = "DateHeaderWithRedux";

export default DateHeaderWithRedux;
