import {
  IDeviceTransfer,
  ITransferDevice,
  IDeviceTransferParams,
  IHistoryDevice,
} from "@/app/(main)/thiet-bi/danh-sach-thiet-bi/type";
import { DataConstant } from "@/constant";
import { DEFAULT_PAGINATION_SKIP_TAKE } from "@/constant/app.const";
import { IDevice, IDeviceParams } from "@/models/eduDevice.model";
import { IPaginationModel } from "@/models/response.model";
import { rootReducer } from "@/redux/reducer";
import { RootState } from "@/redux/store";
import {
  createSelector,
  createSlice,
  PayloadAction,
  WithSlice,
} from "@reduxjs/toolkit";
import { v4 as uuid } from "uuid";

export interface IInitialState {
  deviceSelected: IDevice[];
  transferDevice: IDevice[];
  transferInput: ITransferDevice[];
  isGroupByDefinition: number;
  deviceFilter: IDeviceParams;
  deviceTransfer: IDeviceTransfer[];
  totalDeviceTransfer: number;
  historyDevice?: IHistoryDevice;
  cancelIds: number[];
  deviceTransferParams: IDeviceTransferParams;
  deviceTransferPagination: IPaginationModel;
  resetRowSelected: IDevice[];
}

export const DEFAULT_DEVICE_FILTER: IDeviceParams = {
  isAvailable: true,
};

export const DEFAULT_DEVICE_TRANSFER_PARAMS: IDeviceTransferParams = {
  deviceDefinitionIds: undefined,
  deviceIds: undefined,
  searchKey: undefined,
};

const initialState: IInitialState = {
  deviceSelected: [],
  transferDevice: [],
  transferInput: [],
  isGroupByDefinition: DataConstant.BOOLEAN_TYPE.true,
  deviceFilter: {},
  deviceTransfer: [],
  totalDeviceTransfer: 0,
  historyDevice: undefined,
  cancelIds: [],
  deviceTransferParams: DEFAULT_DEVICE_TRANSFER_PARAMS,
  deviceTransferPagination: DEFAULT_PAGINATION_SKIP_TAKE,
  resetRowSelected: [],
};

export const selectorDeviceSelected = createSelector(
  [(state: RootState) => state.device?.deviceSelected ?? []],
  (deviceSelected) => deviceSelected
);

export const selectorTransferDevice = createSelector(
  [(state: RootState) => state.device?.transferDevice ?? []],
  (transferDevice) => transferDevice
);

export const selectorTransferInput = createSelector(
  [(state: RootState) => state.device?.transferInput ?? []],
  (transferInput) => transferInput
);

export const selectorTransferInputByDeviceId = (deviceId: number) =>
  createSelector(
    [(state: RootState) => state.device?.transferInput ?? []],
    (transferInput) => transferInput.find((item) => item.deviceId === deviceId)
  );

export const selectorDeviceFilter = createSelector(
  [(state: RootState) => state.device?.deviceFilter ?? {}],
  (deviceFilter) => deviceFilter
);

export const selectorDeviceTransfer = createSelector(
  [(state: RootState) => state.device?.deviceTransfer ?? []],
  (deviceTransfer) => deviceTransfer
);

export const selectorTotalDeviceTransfer = createSelector(
  [(state: RootState) => state.device?.totalDeviceTransfer ?? 0],
  (totalDeviceTransfer) => totalDeviceTransfer
);

export const selectorIsGroupByDefinition = createSelector(
  [
    (state: RootState) =>
      state.device?.isGroupByDefinition ?? DataConstant.BOOLEAN_TYPE.true,
  ],
  (isGroupByDefinition) => isGroupByDefinition
);

export const selectorCancelIds = createSelector(
  [(state: RootState) => state.device?.cancelIds ?? []],
  (cancelIds) => cancelIds
);

export const selectorHistoryDevice = createSelector(
  [(state: RootState) => state.device?.historyDevice],
  (historyDevice) => historyDevice
);

export const selectorDeviceTransferParams = createSelector(
  [(state: RootState) => state.device?.deviceTransferParams],
  (deviceTransferParams) => deviceTransferParams
);

export const selectorDeviceTransferPagination = createSelector(
  [(state: RootState) => state.device?.deviceTransferPagination],
  (deviceTransferPagination) => deviceTransferPagination
);

export const selectorResetRowSelected = createSelector(
  [(state: RootState) => state.device?.resetRowSelected],
  (resetRowSelected) => resetRowSelected
);

const reducers = {
  getHistoryDevice: (
    state: IInitialState,
    action: PayloadAction<{
      id: number;
      params;
    }>
  ) => {},
  getHistoryDeviceSuccess: (
    state: IInitialState,
    action: PayloadAction<IHistoryDevice>
  ) => {
    state.historyDevice = {
      ...action.payload,
      transactionHistories: action.payload.transactionHistories.map((item) => ({
        ...item,
        id: uuid(),
      })),
      borrowHistories: action.payload.borrowHistories.map((item) => ({
        ...item,
        id: uuid(),
      })),
    };
  },
  getDeviceTransfer: (
    state: IInitialState,
    action: PayloadAction<IDeviceTransferParams & Partial<IPaginationModel>>
  ) => {},
  getDeviceTransferSuccess: (
    state: IInitialState,
    action: PayloadAction<{ data: IDeviceTransfer[]; totalCount: number }>
  ) => {
    state.deviceTransfer = action.payload.data;
    state.totalDeviceTransfer = action.payload.totalCount;
  },
  setDeviceSelected: (
    state: IInitialState,
    action: PayloadAction<IDevice[]>
  ) => {
    state.deviceSelected = action.payload;
  },
  getDeviceCombo: (
    state: IInitialState,
    action: PayloadAction<IDeviceParams>
  ) => {},
  setTransferDevice: (
    state: IInitialState,
    action: PayloadAction<IDevice[]>
  ) => {
    state.transferDevice = action.payload;
    state.transferInput = action.payload.map((device) => ({
      deviceId: device.id,
      totalAvailable: device.totalBorrowReady,
      totalBroken: device.totalBroken,
      totalLost: device.totalLost,
    }));
  },
  transferDevice: (state: IInitialState) => {
    state.transferDevice = state.deviceSelected;
    state.transferInput = state.transferDevice.map((device) => ({
      deviceId: device.id,
      totalAvailable: device.totalBorrowReady,
      totalBroken: device.totalBroken,
      totalLost: device.totalLost,
    }));
  },
  deleteTransferDevice: (
    state: IInitialState,
    action: PayloadAction<number>
  ) => {
    state.transferDevice = state.transferDevice.filter(
      (device) => device.id !== action.payload
    );
    state.transferInput = state.transferInput.filter(
      (device) => device.deviceId !== action.payload
    );
  },
  setIsGroupByDefinition: (
    state: IInitialState,
    action: PayloadAction<number>
  ) => {
    state.isGroupByDefinition = action.payload;
  },
  setDeviceTransferParams: (
    state: IInitialState,
    action: PayloadAction<IDeviceTransferParams>
  ) => {
    state.deviceTransferParams = action.payload;
  },
  setDeviceTransferPagination: (
    state: IInitialState,
    action: PayloadAction<IPaginationModel>
  ) => {
    state.deviceTransferPagination = action.payload;
  },
  resetTransfer: (state: IInitialState) => {
    state.transferInput = [];
    state.transferDevice = [];
  },
  resetCancelIds: (state: IInitialState) => {
    state.cancelIds = [];
    state.deviceTransfer = [];
    state.totalDeviceTransfer = 0;
    state.deviceTransferParams = DEFAULT_DEVICE_TRANSFER_PARAMS;
    state.deviceTransferPagination = DEFAULT_PAGINATION_SKIP_TAKE;
  },
  changeTransferDevice: (
    state: IInitialState,
    action: PayloadAction<ITransferDevice>
  ) => {
    const index = state.transferInput.findIndex(
      (device) => device.deviceId === action.payload.deviceId
    );

    if (index !== -1) {
      state.transferInput[index] = {
        ...state.transferInput[index],
        ...action.payload,
      };
    } else {
      state.transferInput = [...state.transferInput, action.payload];
    }
  },
  changeDeviceFilterWithKey: (
    state: IInitialState,
    action: PayloadAction<{ key: keyof IDeviceParams; value: any }>
  ) => {
    state.deviceFilter = {
      ...state.deviceFilter,
      [action.payload.key]: action.payload.value,
    };
  },
  selectRow: (state: IInitialState, action: PayloadAction<number>) => {
    const index = state.cancelIds.findIndex((id) => id === action.payload);
    if (index !== -1) {
      state.cancelIds = state.cancelIds.filter((id) => id !== action.payload);
    } else {
      state.cancelIds = [...state.cancelIds, action.payload];
    }
  },
  selectAllRow: (state: IInitialState, action: PayloadAction<boolean>) => {
    if (action.payload) {
      state.cancelIds = state.deviceTransfer.map((item) => item.id);
    } else {
      state.cancelIds = [];
    }
  },
  resetRecordLoss: (state: IInitialState) => {
    state.deviceFilter = DEFAULT_DEVICE_FILTER;
    state.isGroupByDefinition = DataConstant.BOOLEAN_TYPE.true;
  },
  resetDevice: (state: IInitialState) => {
    state.deviceSelected = [];
    state.transferDevice = [];
    state.transferInput = [];
    state.isGroupByDefinition = DataConstant.BOOLEAN_TYPE.true;
    state.deviceFilter = DEFAULT_DEVICE_FILTER;
    state.cancelIds = [];
    state.deviceTransfer = [];
    state.totalDeviceTransfer = 0;
    state.historyDevice = undefined;
  },
  resetHistoryDevice: (state: IInitialState) => {
    state.historyDevice = undefined;
  },
  resetRowSelected: (state: IInitialState) => {
    state.resetRowSelected = [];
  },
};

const deviceSlice = createSlice({
  name: "device",
  initialState,
  reducers,
});

declare module "@/redux/reducer" {
  export interface LazyLoadedSlices extends WithSlice<typeof deviceSlice> {}
}

const injectedDeviceSlice = deviceSlice.injectInto(rootReducer);

export const deviceActions = injectedDeviceSlice.actions;
