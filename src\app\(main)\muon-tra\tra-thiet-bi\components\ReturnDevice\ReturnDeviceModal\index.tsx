"use client";

import { AppModal } from "@/components/common";
import { Button } from "@mui/material";
import { memo, useEffect, useState } from "react";
import { IReturnDeviceList } from "../../../returnDevice.model";
import ReturnDeviceTable from "./ReturnDeviceTable";
import useReturnDevice from "../../../hooks/useReturnDevice";
import { useAppDispatch, useAppSelector } from "@/redux/hook";
import {
  selectEditedRows,
  returnDeviceActions,
} from "../../../returnDevice.slice";
import { systemActions } from "@/redux/system.slice";

const ReturnDeviceModal = ({
  isOpen,
  onClose,
  selectedRows = [],
  fetchCurrentData,
}: {
  isOpen: boolean;
  onClose: () => void;
  selectedRows?: IReturnDeviceList[];
  fetchCurrentData?: () => void;
}) => {
  const dispatch = useAppDispatch();
  const editedData = useAppSelector(selectEditedRows);
  const [approveTableSelectedRows, setApproveTableSelectedRows] = useState<
    IReturnDeviceList[]
  >([]);
  const { returnDevices } = useReturnDevice();

  useEffect(() => {
    dispatch(systemActions.getTeacherComboList());
  }, [dispatch]);

  const handleCloseModal = () => {
    onClose();
    dispatch(returnDeviceActions.resetEditedData());
    setApproveTableSelectedRows([]);
  };

  const handleSubmit = async () => {
    const updatedRows = approveTableSelectedRows.map((row) => {
      const editedValues = editedData[row.id];
      if (editedValues) {
        return {
          ...row,
          totalBroken: editedValues.totalBroken ?? row.totalBroken,
          totalLost: editedValues.totalLost ?? row.totalLost,
          totalConsumed: editedValues.totalConsumed ?? row.totalConsumed,
          notes: editedValues.notes ?? row.notes,
          borrowReturnDate:
            editedValues.borrowReturnDate ?? row.borrowReturnDate,
        };
      }
      return row;
    });

    returnDevices(updatedRows, () => {
      handleCloseModal();
      fetchCurrentData?.();
    });
  };

  // TODO: print receipt
  const handleSubmitAndPrint = async () => {
    const updatedRows = approveTableSelectedRows.map((row) => {
      const editedValues = editedData[row.id];
      if (editedValues) {
        return {
          ...row,
          totalBroken: editedValues.totalBroken ?? row.totalBroken,
          totalLost: editedValues.totalLost ?? row.totalLost,
          totalConsumed: editedValues.totalConsumed ?? row.totalConsumed,
          notes: editedValues.notes ?? row.notes,
          borrowReturnDate:
            editedValues.borrowReturnDate ?? row.borrowReturnDate,
        };
      }
      return row;
    });

    returnDevices(updatedRows, () => {
      // TODO: print receipt
      handleCloseModal();
      fetchCurrentData?.();
    });
  };

  return (
    <AppModal
      isOpen={isOpen}
      onClose={handleCloseModal}
      modalTitleProps={{
        title: "Danh sách thiết bị trả",
      }}
      fullWidth
      maxWidth="xl"
      modalContentProps={{
        sx: {
          pt: 0,
          pb: 0,
          display: "flex",
          flexDirection: "column",
        },
        content: (
          <ReturnDeviceTable
            selectedRows={selectedRows}
            onSelectedRowsChange={setApproveTableSelectedRows}
            approveTableSelectedRows={approveTableSelectedRows}
          />
        ),
      }}
      modalActionsProps={{
        children: (
          <>
            <Button
              variant="outlined"
              color="secondary"
              onClick={handleCloseModal}
            >
              Đóng
            </Button>
            <Button
              type="submit"
              variant="contained"
              onClick={handleSubmitAndPrint}
            >
              Ghi trả và in phiếu xác nhận
            </Button>
            <Button variant="contained" color="primary" onClick={handleSubmit}>
              Ghi trả
            </Button>
          </>
        ),
      }}
      sx={{
        "& .MuiDialog-paper": {
          height: "100%",
        },
      }}
    />
  );
};

export default memo(ReturnDeviceModal);
