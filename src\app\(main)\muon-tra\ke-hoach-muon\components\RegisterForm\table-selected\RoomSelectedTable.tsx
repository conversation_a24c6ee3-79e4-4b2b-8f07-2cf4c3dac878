import { AppFormAutocomplete, AppTable } from "@/components/common";
import { ColumnDef } from "@tanstack/react-table";
import React, {
  forwardRef,
  memo,
  useCallback,
  useImperativeHandle,
  useMemo,
  useState,
} from "react";
import {
  IBorrowRequestAction,
  PURPOSE_USE_ROOM_LIST,
} from "../../../borrowRequestModel";
import { useFieldArray, useFormContext } from "react-hook-form";
import { v4 as uuid } from "uuid";
import DeleteCell from "@/components/common/table/cell/DeleteCell";
import { useAppSelector } from "@/redux/hook";
import { selectRoomList, selectSubjectList } from "@/redux/system.slice";
import { IconButton, Stack, Typography } from "@mui/material";
import EditCell from "@/components/common/table/cell/EditCell";
import dynamic from "next/dynamic";
import { TABLE_MODAL_FULL_HEIGHT } from "@/constant/app.const";
import { PlusIcon } from "@/components/icons";
import {
  BOR<PERSON>W_TYPE,
  BorrowStatusEnum,
  IBorrowRequestRoom,
} from "@/models/eduDevice.model";
const ChooseDeviceOfRoomModal = dynamic(
  () => import("../choose-device-for-room/ChooseDeviceOfRoomModal"),
  { ssr: false }
);
const RoomSelectedTable = forwardRef((props, ref: React.Ref<RoomRefProps>) => {
  const { control, getValues } = useFormContext<IBorrowRequestAction>();
  const { fields, append, remove } = useFieldArray({
    control,
    name: "borrowRequestRooms",
  });

  const [idOpen, setIdOpen] = useState<string | number>();

  const handleClose = useCallback(() => setIdOpen(undefined), []);

  const column = useMemo(() => getColumn(remove, setIdOpen), [remove]);
  useImperativeHandle(
    ref,
    () => {
      return {
        addRoom: () => {
          const borrowFromDate = getValues("borrowFromDate");
          const borrowToDate = getValues("borrowToDate");
          const id = uuid();
          append({
            id,
            roomId: null as any,
            roomName: "",
            periodIds: [],
            subjectId: null as any,
            subjectName: "",
            schoolClassIds: [],
            fromDate: borrowFromDate,
            toDate: borrowToDate,
            roomDeviceGuid: id,
            purpose: null,
            borrowType: BORROW_TYPE.longTerm,
            status: BorrowStatusEnum.Register,
          });
        },
      };
    },
    []
  );

  return (
    <>
      <AppTable
        columns={column}
        data={fields}
        totalData={fields.length}
        hasDefaultPagination
        {...TABLE_MODAL_FULL_HEIGHT}
      />
      <ChooseDeviceOfRoomModal
        isOpen={Boolean(idOpen)}
        idSelected={idOpen}
        onClose={handleClose}
      />
    </>
  );
});

export default RoomSelectedTable;

const getColumn = (remove, setIdOpen): ColumnDef<IBorrowRequestRoom>[] => [
  { id: "index", header: "STT", cell: ({ row }) => row.index + 1, size: 50 },
  {
    id: "delete",
    header: "Xóa",
    cell: ({ row }) => (
      <DeleteCell
        disabled={row.original.status !== BorrowStatusEnum.Register}
        onClick={() => remove(row.index)}
      />
    ),
    size: 50,
  },
  {
    id: "room",
    header: "Kho phòng",
    cell: ({ row }) => (
      <RoomSelectEditForm
        disabled={row.original.status !== BorrowStatusEnum.Register}
        rowIndex={row.index}
      />
    ),
    size: 250,
  },
  {
    id: "device",
    header: "DSTB mượn kèm",
    cell: ({ row }) => (
      <BorrowDeviceCell
        rowIndex={row.index}
        rowId={row.original.roomDeviceGuid}
        setIdOpen={setIdOpen}
        disabled={row.original.status !== BorrowStatusEnum.Register}
      />
    ),
    size: 500,
  },
  {
    id: "sj",
    header: "Môn học",
    cell: ({ row }) => (
      <SubjectSelectEditForm
        disabled={row.original.status !== BorrowStatusEnum.Register}
        rowIndex={row.index}
      />
    ),
    size: 250,
  },
  {
    id: "target",
    header: "Mục đích",
    cell: ({ row }) => (
      <TargetSelectEditForm
        disabled={row.original.status !== BorrowStatusEnum.Register}
        rowIndex={row.index}
      />
    ),
    size: 250,
  },
];

export type RoomRefProps = {
  addRoom: () => void;
};

export const RoomSelectEditForm = memo(
  ({ rowIndex, disabled }: { rowIndex; disabled?: boolean }) => {
    const roomList = useAppSelector(selectRoomList);
    const roomFuncList = useMemo(
      () => roomList.filter((item) => Boolean(item.isFunctionalClassroom)),
      [roomList]
    );
    const {
      control,
      formState: { errors },
    } = useFormContext();

    const fieldError = errors?.borrowRequestRooms?.[rowIndex]?.roomId;

    return (
      <AppFormAutocomplete
        control={control}
        name={`borrowRequestRooms.${rowIndex}.roomId`}
        autocompleteProps={{
          disabled,
          textFieldProps: {
            error: Boolean(fieldError),
            helperText: fieldError?.message,
          },
        }}
        options={roomFuncList}
      />
    );
  }
);

export const SubjectSelectEditForm = memo(
  ({ rowIndex, disabled }: { rowIndex; disabled }) => {
    const subjectList = useAppSelector(selectSubjectList);

    const {
      control,
      formState: { errors },
    } = useFormContext();

    const fieldError = errors?.borrowRequestRooms?.[rowIndex]?.subjectId;

    return (
      <AppFormAutocomplete
        control={control}
        name={`borrowRequestRooms.${rowIndex}.subjectId`}
        autocompleteProps={{
          disabled,
          textFieldProps: {
            error: Boolean(fieldError),
            helperText: fieldError?.message,
          },
        }}
        options={subjectList}
      />
    );
  }
);

export const TargetSelectEditForm = memo(
  ({ rowIndex, disabled }: { rowIndex; disabled }) => {
    const {
      control,
      formState: { errors },
    } = useFormContext();

    const fieldError = errors?.borrowRequestRooms?.[rowIndex]?.purpose;

    return (
      <AppFormAutocomplete
        control={control}
        name={`borrowRequestRooms.${rowIndex}.purpose`}
        autocompleteProps={{
          disabled,
          textFieldProps: {
            error: Boolean(fieldError),
            helperText: fieldError?.message,
          },
        }}
        options={PURPOSE_USE_ROOM_LIST}
      />
    );
  }
);

const BorrowDeviceCell = memo(
  ({
    rowIndex,
    rowId,
    disabled,
    setIdOpen,
  }: {
    rowIndex: number;
    rowId?: number | string | null;
    setIdOpen;
    disabled;
  }) => {
    const { control } = useFormContext<IBorrowRequestAction>();
    const { fields } = useFieldArray({ control, name: "borrowRequestDevices" });

    const deviceOfRoom = fields.filter((item) => item.roomDeviceGuid === rowId);
    return (
      <Stack
        justifyContent="space-between"
        direction="row"
        alignItems="center"
        spacing={1}
      >
        <Typography>
          {deviceOfRoom
            .map((d) => `${d.deviceName} (${d.quantity})`)
            .join(", ")}
        </Typography>
        {deviceOfRoom.length ? (
          <EditCell disabled={disabled} onClick={() => setIdOpen(rowId)} />
        ) : (
          <IconButton
            disabled={disabled}
            sx={{
              p: 0,
              width: 24,
              height: 24,
            }}
            onClick={() => setIdOpen(rowId)}
          >
            <PlusIcon />
          </IconButton>
        )}
      </Stack>
    );
  }
);
