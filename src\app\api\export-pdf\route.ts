import { NextRequest, NextResponse } from "next/server";
import { chromium } from "playwright";

export async function POST(request: NextRequest) {
  try {
    const {
      tableData,
      columns,
      orientation = "portrait",
      title = "Báo cáo",
    } = await request.json();

    // Debug: Log dữ liệu columns để kiểm tra
    console.log("Columns received:", JSON.stringify(columns, null, 2));

    // Tạo HTML content cho bảng với hỗ trợ cột nhóm (tương tự Excel)
    const generateTableHTML = (data: any[], cols: any[]) => {
      // Tính độ sâu tối đa của header (giống Excel)
      const getMaxDepth = (columns: any[], currentDepth = 1): number => {
        if (!columns || columns.length === 0) return currentDepth;

        return Math.max(
          ...columns.map((col) => {
            if (col.child && col.child.length > 0) {
              return getMaxDepth(col.child, currentDepth + 1);
            }
            return currentDepth;
          })
        );
      };

      const maxDepth = getMaxDepth(cols);

      // Tạo header HTML với logic đệ quy (giống Excel createHeader)
      const generateHeaderHTML = (
        columns: any[],
        currentRow = 0,
        processedCells: Set<string> = new Set()
      ): string[] => {
        const headerRows: string[] = Array(maxDepth)
          .fill("")
          .map(() => "");

        const processColumn = (
          col: any,
          colIndex: number,
          currentCol: number
        ) => {
          const cellKey = `${currentRow}-${currentCol}`;
          if (processedCells.has(cellKey)) return currentCol;

          const isGroup = col.child && col.child.length > 0;

          if (isGroup) {
            // Cột nhóm: tính colspan và đặt rowspan = 1
            const colspan = calculateColspan(col.child);
            const cellHTML = `<th style="border: 1px solid #ddd; padding: 8px; text-align: center; background-color: #f2f2f2; font-weight: bold;" colspan="${colspan}" rowspan="1">${
              col.name || col.header || ""
            }</th>`;

            headerRows[currentRow] += cellHTML;

            // Đánh dấu các cell đã được xử lý
            for (let c = currentCol; c < currentCol + colspan; c++) {
              processedCells.add(`${currentRow}-${c}`);
            }

            // Xử lý đệ quy các cột con ở row tiếp theo
            let childCol = currentCol;
            col.child.forEach((childColumn: any) => {
              childCol = processChildColumn(
                childColumn,
                currentRow + 1,
                childCol,
                headerRows,
                processedCells
              );
            });

            return currentCol + colspan;
          } else {
            // Cột lá: rowspan = maxDepth - currentRow
            const rowspan = maxDepth - currentRow;
            const cellHTML = `<th style="border: 1px solid #ddd; padding: 8px; text-align: center; background-color: #f2f2f2; font-weight: bold;" rowspan="${rowspan}">${
              col.name || col.header || ""
            }</th>`;

            headerRows[currentRow] += cellHTML;

            // Đánh dấu các cell đã được xử lý
            for (let r = currentRow; r < maxDepth; r++) {
              processedCells.add(`${r}-${currentCol}`);
            }

            return currentCol + 1;
          }
        };

        const processChildColumn = (
          col: any,
          row: number,
          colIndex: number,
          rows: string[],
          processed: Set<string>
        ): number => {
          const cellKey = `${row}-${colIndex}`;
          if (processed.has(cellKey)) return colIndex;

          const isGroup = col.child && col.child.length > 0;

          if (isGroup) {
            const colspan = calculateColspan(col.child);
            const cellHTML = `<th style="border: 1px solid #ddd; padding: 8px; text-align: center; background-color: #f2f2f2; font-weight: bold;" colspan="${colspan}" rowspan="1">${
              col.name || col.header || ""
            }</th>`;

            rows[row] += cellHTML;

            for (let c = colIndex; c < colIndex + colspan; c++) {
              processed.add(`${row}-${c}`);
            }

            let childCol = colIndex;
            col.child.forEach((childColumn: any) => {
              childCol = processChildColumn(
                childColumn,
                row + 1,
                childCol,
                rows,
                processed
              );
            });

            return colIndex + colspan;
          } else {
            const rowspan = maxDepth - row;
            const cellHTML = `<th style="border: 1px solid #ddd; padding: 8px; text-align: center; background-color: #f2f2f2; font-weight: bold;" rowspan="${rowspan}">${
              col.name || col.header || ""
            }</th>`;

            rows[row] += cellHTML;

            for (let r = row; r < maxDepth; r++) {
              processed.add(`${r}-${colIndex}`);
            }

            return colIndex + 1;
          }
        };

        // Tính colspan cho cột nhóm
        const calculateColspan = (children: any[]): number => {
          return children.reduce((sum, child) => {
            if (child.child && child.child.length > 0) {
              return sum + calculateColspan(child.child);
            }
            return sum + 1;
          }, 0);
        };

        // Xử lý tất cả columns ở level đầu tiên
        let currentCol = 0;
        columns.forEach((col, index) => {
          currentCol = processColumn(col, index, currentCol);
        });

        return headerRows.map((row) => `<tr>${row}</tr>`);
      };

      // Tạo data rows (giống Excel mapRowData)
      const generateDataRows = (data: any[], columns: any[]): string => {
        return data
          .map((row) => {
            const cells: string[] = [];

            // Hàm đệ quy để lấy dữ liệu từ các cột (giống Excel extractData)
            const extractData = (cols: any[]) => {
              cols.forEach((col) => {
                if (col.child && col.child.length > 0) {
                  extractData(col.child); // Xử lý cột lồng nhau
                } else {
                  // Lấy giá trị từ row data
                  const value =
                    row[col.field || col.key || col.accessorKey] || "";
                  cells.push(
                    `<td style="border: 1px solid #ddd; padding: 8px; text-align: left;">${value}</td>`
                  );
                }
              });
            };

            extractData(columns);
            return `<tr>${cells.join("")}</tr>`;
          })
          .join("");
      };

      const headerRows = generateHeaderHTML(columns);
      const dataRows = generateDataRows(data, columns);

      // Debug: Log header rows để kiểm tra
      console.log("Header rows:", headerRows.join(""));

      return `
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="UTF-8">
          <title>${title}</title>
          <style>
            body {
              font-family: Arial, sans-serif;
              margin: 20px;
              font-size: 12px;
            }
            .header {
              text-align: center;
              margin-bottom: 20px;
              font-size: 18px;
              font-weight: bold;
            }
            table {
              width: 100%;
              border-collapse: collapse;
              margin-top: 10px;
            }
            th, td {
              border: 1px solid #ddd;
              padding: 8px;
              text-align: left;
            }
            th {
              background-color: #f2f2f2;
              font-weight: bold;
            }
            .footer {
              margin-top: 20px;
              text-align: right;
              font-size: 10px;
              color: #666;
            }
          </style>
        </head>
        <body>
          <div class="header">${title}</div>
          <table>
            <thead>
              ${headerRows.join("")}
            </thead>
            <tbody>
              ${dataRows}
            </tbody>
          </table>
          <div class="footer">
            Xuất ngày: ${new Date().toLocaleDateString("vi-VN")}
          </div>
        </body>
        </html>
      `;
    };

    const htmlContent = generateTableHTML(tableData, columns);

    // Debug: Log HTML content để kiểm tra
    console.log("Generated HTML:", htmlContent);

    // Khởi tạo browser
    const browser = await chromium.launch({
      headless: true,
    });

    const page = await browser.newPage();

    // Set content HTML
    await page.setContent(htmlContent);

    // Tạo PDF
    const pdfBuffer = await page.pdf({
      format: "A4",
      landscape: orientation === "landscape",
      printBackground: true,
      margin: {
        top: "20mm",
        right: "20mm",
        bottom: "20mm",
        left: "20mm",
      },
    });

    await browser.close();

    // Trả về PDF
    return new NextResponse(pdfBuffer, {
      headers: {
        "Content-Type": "application/pdf",
        "Content-Disposition": `attachment; filename="${title.replace(
          /[^a-zA-Z0-9]/g,
          "_"
        )}_${new Date().toISOString().split("T")[0]}.pdf"`,
      },
    });
  } catch (error) {
    console.error("Lỗi khi tạo PDF:", error);
    return NextResponse.json(
      { error: "Có lỗi xảy ra khi tạo PDF" },
      { status: 500 }
    );
  }
}
