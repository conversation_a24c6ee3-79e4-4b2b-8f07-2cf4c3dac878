import { NextRequest, NextResponse } from "next/server";
import { chromium } from "playwright";

export async function POST(request: NextRequest) {
  try {
    const {
      tableData,
      columns,
      orientation = "portrait",
      title = "Báo cáo",
    } = await request.json();

    // Debug: Log dữ liệu columns để kiểm tra
    console.log("Columns received:", JSON.stringify(columns, null, 2));

    // Tạo HTML content cho bảng với hỗ trợ cột nhóm
    const generateTableHTML = (data: any[], cols: any[]) => {
      // Tạo header rows cho cột nhóm
      const generateHeaderRows = (columns: any[]): string => {
        const maxDepth = getMaxDepth(columns);
        const headerRows: string[] = [];

        for (let rowIndex = 0; rowIndex < maxDepth; rowIndex++) {
          const cells: string[] = [];

          columns.forEach((col) => {
            const cell = generateHeaderCell(col, rowIndex, maxDepth);
            if (cell) cells.push(cell);
          });

          headerRows.push(`<tr>${cells.join("")}</tr>`);
        }

        return headerRows.join("");
      };

      const generateHeaderCell = (
        col: any,
        rowIndex: number,
        maxDepth: number
      ): string => {
        const isGroup = col.isGroup || (col.child && col.child.length > 0);
        const isLeaf = !isGroup;

        if (isGroup) {
          if (rowIndex === 0) {
            // Header row đầu tiên - hiển thị tên group
            return `<th style="border: 1px solid #ddd; padding: 8px; text-align: center; background-color: #f2f2f2; font-weight: bold;" rowspan="${maxDepth}" colspan="${
              col.colspan || 1
            }">${col.name || col.header}</th>`;
          } else {
            // Các row tiếp theo - không hiển thị gì cho group
            return "";
          }
        } else {
          if (rowIndex === maxDepth - 1) {
            // Row cuối cùng - hiển thị tên cột leaf
            return `<th style="border: 1px solid #ddd; padding: 8px; text-align: center; background-color: #f2f2f2; font-weight: bold;">${
              col.name || col.header
            }</th>`;
          } else {
            // Các row trước - không hiển thị gì cho leaf
            return "";
          }
        }
      };

      const getMaxDepth = (columns: any[]): number => {
        let maxDepth = 1;

        const traverse = (cols: any[], depth: number) => {
          maxDepth = Math.max(maxDepth, depth);

          cols.forEach((col) => {
            if (col.isGroup || (col.child && col.child.length > 0)) {
              traverse(col.child || col.children || [], depth + 1);
            }
          });
        };

        traverse(columns, 1);
        return maxDepth;
      };

      // Tạo data rows
      const generateDataRows = (data: any[], columns: any[]): string => {
        return data
          .map((row) => {
            const cells: string[] = [];

            const traverseColumns = (cols: any[]) => {
              cols.forEach((col) => {
                if (col.isGroup || (col.child && col.child.length > 0)) {
                  traverseColumns(col.child || col.children || []);
                } else {
                  const value = row[col.field || col.key || col.accessorKey];
                  cells.push(
                    `<td style="border: 1px solid #ddd; padding: 8px; text-align: left;">${
                      value || ""
                    }</td>`
                  );
                }
              });
            };

            traverseColumns(columns);
            return `<tr>${cells.join("")}</tr>`;
          })
          .join("");
      };

      const headerRows = generateHeaderRows(columns);
      const dataRows = generateDataRows(data, columns);

      // Debug: Log header rows để kiểm tra
      console.log("Header rows:", headerRows);

      return `
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="UTF-8">
          <title>${title}</title>
          <style>
            body {
              font-family: Arial, sans-serif;
              margin: 20px;
              font-size: 12px;
            }
            .header {
              text-align: center;
              margin-bottom: 20px;
              font-size: 18px;
              font-weight: bold;
            }
            table {
              width: 100%;
              border-collapse: collapse;
              margin-top: 10px;
            }
            th, td {
              border: 1px solid #ddd;
              padding: 8px;
              text-align: left;
            }
            th {
              background-color: #f2f2f2;
              font-weight: bold;
            }
            .footer {
              margin-top: 20px;
              text-align: right;
              font-size: 10px;
              color: #666;
            }
          </style>
        </head>
        <body>
          <div class="header">${title}</div>
          <table>
            <thead>
              ${headerRows}
            </thead>
            <tbody>
              ${dataRows}
            </tbody>
          </table>
          <div class="footer">
            Xuất ngày: ${new Date().toLocaleDateString("vi-VN")}
          </div>
        </body>
        </html>
      `;
    };

    const htmlContent = generateTableHTML(tableData, columns);

    // Debug: Log HTML content để kiểm tra
    console.log("Generated HTML:", htmlContent);

    // Khởi tạo browser
    const browser = await chromium.launch({
      headless: true,
    });

    const page = await browser.newPage();

    // Set content HTML
    await page.setContent(htmlContent);

    // Tạo PDF
    const pdfBuffer = await page.pdf({
      format: "A4",
      landscape: orientation === "landscape",
      printBackground: true,
      margin: {
        top: "20mm",
        right: "20mm",
        bottom: "20mm",
        left: "20mm",
      },
    });

    await browser.close();

    // Trả về PDF
    return new NextResponse(pdfBuffer, {
      headers: {
        "Content-Type": "application/pdf",
        "Content-Disposition": `attachment; filename="${title.replace(
          /[^a-zA-Z0-9]/g,
          "_"
        )}_${new Date().toISOString().split("T")[0]}.pdf"`,
      },
    });
  } catch (error) {
    console.error("Lỗi khi tạo PDF:", error);
    return NextResponse.json(
      { error: "Có lỗi xảy ra khi tạo PDF" },
      { status: 500 }
    );
  }
}
