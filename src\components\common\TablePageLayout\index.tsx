"use client";

import ContentPage, { ContentPageProps } from "./ContentPage";
import { ITableRef, TableProviderProps } from "./type";
import {
  ModalConfigProvider,
  ModalConfigContextProps,
} from "./modal-store/ModalActionContext";
import React, { JSX, memo, forwardRef } from "react";
import { TableProvider } from "./table-store/TableProvider";

const TablePageLayout = forwardRef(
  <T,>(
    {
      apiUrl,
      methodFetch,
      tableProps,
      filterConfig,
      formConfig,
      actions,
      CreateModalComponent,
      EditModalComponent,
      hasReload,
      formatData,
      fetchAll,
      customActions,
      visibleCol,
      cleanDataFormFiled,
      onResetFilter,
      formatDetailData,
      filterCustom,
      collapseFilterCustom,
      pageContent,
      createFormContent,
      hasBaseCol,
      excelConfig,
    }: TablePageLayoutProps<T>,
    ref: React.Ref<ITableRef>
  ) => {
    return (
      <TableProvider<T>
        apiUrl={apiUrl}
        methodFetch={methodFetch}
        formatData={formatData}
        filterConfig={filterConfig}
        fetchAll={fetchAll}
        cleanDataFormFiled={cleanDataFormFiled}
        onResetFilter={onResetFilter}
        excelConfig={excelConfig}
      >
        <ModalConfigProvider
          detailUrl={formConfig?.detailUrl}
          formatDetailData={formatDetailData}
        >
          <ContentPage<T>
            ref={ref}
            hasBaseCol={hasBaseCol}
            hasReload={hasReload}
            formConfig={formConfig}
            actions={actions}
            tableProps={tableProps}
            CreateModalComponent={CreateModalComponent}
            customActions={customActions}
            visibleCol={visibleCol}
            filterCustom={filterCustom}
            collapseFilterCustom={collapseFilterCustom}
            EditModalComponent={EditModalComponent}
            pageContent={pageContent}
            createFormContent={createFormContent}
          />
        </ModalConfigProvider>
      </TableProvider>
    );
  }
);

// Export
export default memo(TablePageLayout) as <T>(
  props: TablePageLayoutProps<T> & { ref?: React.Ref<ITableRef> }
) => JSX.Element;

type TablePageLayoutProps<T> = ContentPageProps<T> &
  Omit<TableProviderProps<T>, "children"> &
  Pick<ModalConfigContextProps, "formatDetailData"> & {};
