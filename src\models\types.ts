import { DataConstant } from "@/constant";
import { SxProps, Theme } from "@mui/material";
import { AddWorksheetOptions, Style, Workbook, Worksheet } from "exceljs";

export interface IStyleProps {
  [x: string]: SxProps<Theme>;
}

export interface ObjectMultiLanguageProps {
  [x: string]: string;
}

export interface KeyAbleProps {
  [key: string]: unknown;
}

export interface IUploadImg {
  categoryTypeId: number;
  createdAt?: string;
  fileType: string;
  groupUnitCode: string;
  id: number;
  isUsing: number;
  keyUpload: string;
  originName: string;
  schoolCode: string;
  size: number;
  updatedAt?: string;
  updatedBy?: number;
  url: string;
  documentUrl?: string;
}

export interface IUploadFile {
  doetCode: number;
  divisionCode: number;
  schoolCode: number;
  groupUnitCode: number;
  fileName: string;
  originName: string;
  fileType: string;
  keyUpload: string;
  size: number;
  url: string;
  categoryTypeId: DataConstant.CATEGORY_ID;
  isUsing: DataConstant.BOOLEAN_TYPE;
  documentId?: null | number;
  id: number;
  createdBy: number;
  updatedBy: number;
  createdAt: string;
  updatedAt: string;
}

export interface ITree {
  parentId: number;
  id: number;
  children?: ITree[];
  order?: number;
  [x: string]: any;
}

export type DateRangeValue = Array<Date | null>;

export interface ITreeData {
  id: number | string;
  name: string;
  status?: DataConstant.STATUS_TYPE;
  children?: ITreeData[];

  [x: string]: any;
}

export type ExcelColumnProps = {
  style?: Partial<Style>;
  name: string;
  key?: string;
  width?: number;
  rowSpan?: number;
  colSpan?: number;
  child?: ExcelColumnProps[];
};

export type ExportExcelProps = {
  sheetId?: string;
  tableName?: string;
  fileName?: string;
  hasTemplate?: boolean;
  configStartColumn?: number;
  notAddEmptyDataRow?: boolean;
  columns: Array<ExcelColumnProps>;
  rows?:
    | any[][]
    | {
        [x: string]: any;
      }[];
  rowHeight?: number;
  options?: Partial<AddWorksheetOptions>;
  onCustomWorkSheet?: (workSheet: Worksheet, workbook: Workbook) => void;
  onCoverSheet?: (workbook: Workbook) => void;
  rowHeightHeader?: number;
  hasIndexCol?: boolean;
};
