import { AppConstant, PathConstant } from "@/constant";
import {
  IDomainInfo,
  ISchool,
  ISchoolYear,
  ISchoolYearConfig,
  IUserInfo,
} from "@/models/app.model";
import { IMenuItemTree, MENU_TYPE, IMenuGroup } from "@/models/menu.model";
import { PayloadAction, createSelector, createSlice } from "@reduxjs/toolkit";
import Cookies from "js-cookie";

import { transformMenuTreeToGroups } from "@/utils/tree.utils";
import { SEMESTER_TYPE, SEMESTER_TYPE_LIST } from "@/constant/data.const";
import { RootState } from "./store";
import { IOption } from "@/components/common";
import dayjs from "dayjs";

/* ------------- Initial State ------------- */
export interface IInitialState {
  isFetching: boolean;
  error: object | string | null;

  isCollapse: boolean;
  schoolInfo: ISchool | null;

  menuSidebar: IMenuItemTree[];
  expandId: null | number;

  domainInfo: null | IDomainInfo;
  userInfo: null | IUserInfo;
  changePassword: {
    isFetching: boolean;
    error: object | string | null;
  };
  changeSchoolYear: {
    isFetching: boolean;
    error: object | string | null;
  };
  schoolYearList: ISchoolYear[];
  schoolYearSelected: IOption | null;
  semesterSelected: IOption | null;
}

const initialState: IInitialState = {
  isFetching: false,
  error: null,

  isCollapse: false,
  schoolInfo: null,

  menuSidebar: [],
  expandId: null,

  domainInfo: null,
  userInfo: null,
  changePassword: {
    isFetching: false,
    error: null,
  },
  changeSchoolYear: {
    isFetching: false,
    error: null,
  },
  schoolYearList: [],
  schoolYearSelected: null,
  semesterSelected: null,
};

/* ------------- Selector ------------- */
export const selectSchoolInfo = (state: RootState) =>
  state.appReducer.schoolInfo;
export const selectMenuSidebar = (state: RootState) =>
  state.appReducer.menuSidebar;
export const selectSchoolYearList = (state: RootState) =>
  state.appReducer.schoolYearList;

export const selectSchoolYearOptions = createSelector(
  [selectSchoolYearList],
  (schoolYearList) =>
    (schoolYearList || []).map((item) => convertYearToOption(item))
);

export const selectSystemMenuItem = createSelector(
  [selectMenuSidebar],
  (menuSidebar) => menuSidebar.find((item) => item.href === PathConstant.SYSTEM)
);
export const selectSystemMenuData = createSelector(
  [selectSystemMenuItem],
  (systemMenuItem): IMenuGroup[] => {
    if (
      systemMenuItem &&
      systemMenuItem.children &&
      systemMenuItem.children.length > 0
    ) {
      return transformMenuTreeToGroups(systemMenuItem.children);
    }
    return [];
  }
);

/* ------------- Reducers ------------- */
const reducers = {
  getDomainInfo: (state: IInitialState) => {
    state.isFetching = true;
    state.error = null;
  },
  getDomainInfoSuccess(
    state: IInitialState,
    action: PayloadAction<IDomainInfo>
  ) {
    state.domainInfo = action.payload;
  },

  getUserInfo: (
    state: IInitialState,
    action: PayloadAction<{ onSuccess?: () => void }>
  ) => {},
  getUserInfoSuccess: (
    state: IInitialState,
    action: PayloadAction<IUserInfo>
  ) => {
    const newUserInfo = action.payload;
    state.userInfo = newUserInfo;
    const semesterLocal = localStorage.getItem(AppConstant.COOKIE_KEY.semester);
    if (newUserInfo?.schoolInfos.length === 1) {
      state.schoolInfo = newUserInfo.schoolInfos[0];
      state.schoolYearSelected = convertYearToOption(
        newUserInfo.schoolInfos[0].schoolYearConfig
      );

      let semester: (typeof SEMESTER_TYPE_LIST)[number] | null = null;

      if (semesterLocal) {
        try {
          semester = JSON.parse(semesterLocal);
        } catch (error) {
          console.error("Invalid semester data in localStorage", error);
        }
      } else {
        const now = dayjs();
        const isBeforeSemester2 = now.isBefore(
          dayjs(state.schoolYearSelected.startOfSemester2)
        );

        semester =
          SEMESTER_TYPE_LIST.find((item) =>
            isBeforeSemester2
              ? item.id === SEMESTER_TYPE.semester1
              : item.id === SEMESTER_TYPE.semester2
          ) || null;

        if (semester) {
          localStorage.setItem(
            AppConstant.COOKIE_KEY.semester,
            JSON.stringify(semester)
          );
        }
      }

      state.semesterSelected = semester;
    }
  },

  getMenuSideBar: (state: IInitialState, action: PayloadAction<MENU_TYPE>) => {
    state.isFetching = true;
  },
  getMenuSideBarSuccess: (
    state: IInitialState,
    action: PayloadAction<IMenuItemTree[]>
  ) => {
    state.isFetching = false;
    state.menuSidebar = action.payload;
    state.error = null;
  },
  toggleSideBar: (state: IInitialState) => {
    state.isCollapse = !state.isCollapse;
    state.expandId = null;
  },
  changeExpandId: (
    state: IInitialState,
    action: PayloadAction<number | null>
  ) => {
    state.expandId = action.payload;
  },

  clearToken: (state: IInitialState) => {
    Cookies.remove(AppConstant.ACCESS_TOKEN);
    Cookies.remove(AppConstant.COOKIE_KEY.orgId);
  },
  logout: (state: IInitialState) => {
    Cookies.remove(AppConstant.ACCESS_TOKEN);
    Cookies.remove(AppConstant.COOKIE_KEY.orgId);
    localStorage.removeItem(AppConstant.COOKIE_KEY.semester);
    state.domainInfo = null;
    state.userInfo = null;
    state.schoolYearSelected = null;
    state.semesterSelected = null;
  },
  changePassword: (
    state: IInitialState,
    action: PayloadAction<{
      password: string;
      newPassword: string;
      confirmPassword: string;
      onSuccess?: () => void;
    }>
  ) => {
    state.changePassword.isFetching = true;
    state.changePassword.error = null;
  },
  changePasswordSuccess: (state: IInitialState) => {
    state.changePassword.isFetching = false;
    state.changePassword.error = null;
  },
  getSchoolYearList: (state: IInitialState) => {
    state.isFetching = true;
    state.error = null;
  },
  getSchoolYearListSuccess: (
    state: IInitialState,
    action: PayloadAction<ISchoolYear[]>
  ) => {
    state.schoolYearList = action.payload;
    state.isFetching = false;
  },
  changeSchoolYear: (
    state: IInitialState,
    action: PayloadAction<{
      schoolYear: IOption;
      semester: IOption;
      onSuccess?: () => void;
    }>
  ) => {
    state.changeSchoolYear.isFetching = true;
    state.changeSchoolYear.error = null;
  },
  changeSchoolYearSuccess: (
    state: IInitialState,
    action: PayloadAction<{
      schoolYear: IOption;
      semester: IOption;
    }>
  ) => {
    state.changeSchoolYear.isFetching = false;
    state.changeSchoolYear.error = null;
    state.schoolYearSelected = action.payload.schoolYear;
    state.semesterSelected = action.payload.semester;
  },

  // Common
  appFailure: (state: IInitialState, action: PayloadAction<any>) => {
    const error = action.payload ? action.payload : {};
    state.isFetching = false;
    state.error = error;
  },
  appReset: (state: IInitialState) => {
    state.isFetching = false;
    state.error = null;

    state.schoolInfo = null;
    state.schoolYearList = [];
    state.isCollapse = false;

    state.menuSidebar = [];
    state.expandId = null;

    state.domainInfo = null;
    state.userInfo = null;
    state.changePassword = {
      isFetching: false,
      error: null,
    };
  },
};

export const appSlice = createSlice({
  name: "appReducer",
  initialState,
  reducers,
});

const appReducer = appSlice.reducer;

export const appActions = appSlice.actions;

export default appReducer;

const convertYearToOption = (
  data: ISchoolYear | ISchoolYearConfig
): IOption => {
  return {
    id: data.year,
    label:
      (data as ISchoolYear).name ?? (data as ISchoolYearConfig).schoolYearName,
  };
};
