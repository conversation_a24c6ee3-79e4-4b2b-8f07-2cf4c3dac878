import { AppModal, GridFormContainer } from "@/components/common";
import AppFormTextField from "@/components/common/form/AppFormTextField";
import AppFormSelectTree from "@/components/common/form/AppFormSelectTree";
import AppFormAutocomplete from "@/components/common/form/AppFormAutocomplete";
import AppFormToggle from "@/components/common/form/AppFormToggle";
import { FormProvider, useForm } from "react-hook-form";
import { IAddMenuConfig } from "@/models/menu.model";
import { Button, Grid } from "@mui/material";
import { useEffect } from "react";
import { AppConstant, DataConstant } from "@/constant";
import { convertDataToAddMenuConfig } from "../helper";
import useMenuActions from "../hooks/useMenuActions";
import { findMenuConfigTree } from "@/utils/tree.utils";
import { useAppSelector } from "@/redux/hook";
import { menuSelectors } from "@/app/(main)/root/quan-ly-menu/store/menu.slice";

const MenuConfigModal = ({
  isOpen,
  onClose,
  menuConfigId,
}: {
  isOpen: boolean;
  onClose: () => void;
  menuConfigId?: number | null;
}) => {
  const menuTreeList = useAppSelector(menuSelectors.menuConfig);
  const filter = useAppSelector(menuSelectors.filter);
  const { handleAddMenu, handleUpdateMenu } = useMenuActions();
  const methods = useForm<IAddMenuConfig>({
    defaultValues: DEFAULT_VALUES,
  });
  const {
    control,
    handleSubmit,
    reset,
    formState: { errors },
  } = methods;

  const handleCloseModal = () => {
    reset(DEFAULT_VALUES);
    onClose();
  };

  const onSubmit = (data: any) => {
    const result = convertDataToAddMenuConfig({
      ...data,
      menuTypeId: filter.menuTypeId,
    });

    if (menuConfigId) {
      handleUpdateMenu(result, menuConfigId, handleCloseModal);
    } else {
      handleAddMenu(result, handleCloseModal);
    }
  };

  useEffect(() => {
    if (menuConfigId && isOpen) {
      const menuConfig = findMenuConfigTree(menuTreeList, menuConfigId);
      if (menuConfig) {
        const convertedData = {
          ...menuConfig,
          status: Boolean(menuConfig.status),
          isSystem: Boolean(menuConfig.isSystem),
          isRoot: Boolean(menuConfig.isRoot),
          isShowSubMenu: Boolean(menuConfig.isShowSubMenu),
          isBetaMenu: Boolean(menuConfig.isBetaMenu),
          parentId: menuConfig.parentId || AppConstant.DEFAULT_TREE,
          menuTypeId: filter.menuTypeId as number,
          groupUnitCodes: findItemsByCode(menuConfig.listGroupsUnitCode),
        };
        reset(convertedData);
      }
    }
  }, [menuConfigId, menuTreeList, isOpen]);

  return (
    <FormProvider {...methods}>
      <AppModal
        isOpen={isOpen}
        onClose={handleCloseModal}
        component={"form"}
        onSubmit={handleSubmit(onSubmit)}
        modalTitleProps={{
          title: "Thêm mới",
        }}
        modalContentProps={{
          content: (
            <GridFormContainer>
              <Grid size={6}>
                <AppFormSelectTree
                  label="Menu cha"
                  nodeChildren="children"
                  control={control}
                  name="parentId"
                  data={menuTreeList}
                  textfieldProps={{
                    autoComplete: "off",
                  }}
                />
              </Grid>
              <Grid size={6}>
                <AppFormTextField
                  control={control}
                  name="name"
                  label="Tên menu"
                  rules={{ required: "Vui lòng nhập tên menu" }}
                  textfieldProps={{
                    error: !!errors.name,
                    helperText: errors.name?.message,
                  }}
                />
              </Grid>
              <Grid size={12}>
                <AppFormTextField
                  control={control}
                  name="href"
                  label="Đường dẫn"
                  rules={{
                    required: "Vui lòng nhập đường dẫn",
                  }}
                  textfieldProps={{
                    error: !!errors.href,
                    helperText: errors.href?.message,
                  }}
                />
              </Grid>
              <Grid size={6}>
                <AppFormTextField control={control} name="icon" label="Icon" />
              </Grid>
              <Grid size={6}>
                <AppFormTextField
                  control={control}
                  textfieldProps={{
                    error: Boolean(errors?.order),
                    helperText: errors?.order?.message,
                    type: "number",
                    slotProps: {
                      htmlInput: {
                        min: 1,
                      },
                    },
                  }}
                  rules={{
                    min: {
                      value: 1,
                      message: "Vui lòng nhập thứ tự lớn hơn 0",
                    },
                  }}
                  label="Thứ tự"
                  name="order"
                />
              </Grid>
              <Grid size={12}>
                <AppFormAutocomplete
                  control={control}
                  name="groupUnitCodes"
                  label="Đối tượng sử dụng"
                  rules={{
                    required: "Vui lòng chọn đối tượng sử dụng",
                  }}
                  options={DataConstant.DON_VI_LIST.filter(
                    (item) => item.id !== DataConstant.DON_VI_TYPE.bo
                  )}
                  multiple
                  autocompleteProps={{
                    textFieldProps: {
                      error: !!errors.groupUnitCodes,
                      helperText: errors.groupUnitCodes?.message,
                    },
                  }}
                />
              </Grid>
              <Grid size={6}>
                <AppFormToggle
                  control={control}
                  name="status"
                  label="Trạng thái hiển thị"
                />
              </Grid>
              <Grid size={6}>
                <AppFormToggle
                  control={control}
                  name="isSystem"
                  label="Là menu hệ thống"
                />
              </Grid>
              <Grid size={6}>
                <AppFormToggle
                  control={control}
                  name="isRoot"
                  label="Dành cho tài khoản kỹ thuật"
                />
              </Grid>
              <Grid size={6}>
                <AppFormToggle
                  control={control}
                  name="isShowSubMenu"
                  label="Hiển thị menu con"
                />
              </Grid>
              <Grid size={6}>
                <AppFormToggle
                  control={control}
                  name="isBetaMenu"
                  label="Là menu dùng thử"
                />
              </Grid>
            </GridFormContainer>
          ),
        }}
        modalActionsProps={{
          children: (
            <>
              <Button
                type="button"
                variant="outlined"
                color="secondary"
                onClick={handleCloseModal}
              >
                Đóng
              </Button>
              <Button type="submit" variant="contained">
                Ghi
              </Button>
            </>
          ),
        }}
      />
    </FormProvider>
  );
};

export default MenuConfigModal;

const DEFAULT_VALUES = {
  name: "",
  icon: "",
  status: true,
  isSystem: false,
  isRoot: false,
  parentId: AppConstant.DEFAULT_TREE,
  menuTypeId: null,
  href: "",
  order: 1,
  groupUnitCodes: [],
  isShowSubMenu: false,
  isBetaMenu: false,
};

const findItemsByCode = (arr: string[]) => {
  const result = DataConstant.DON_VI_LIST.filter((item) =>
    arr.includes(item.schoolCode)
  );
  return result;
};
