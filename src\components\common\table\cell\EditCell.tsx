import { IconButton, IconButtonProps, TableCellProps } from "@mui/material";
import { memo } from "react";
import { EditIcon } from "@/components/icons";

const EditCell = ({ onClick, ...otherProps }: IconButtonProps) => {
  const handleClick = (e) => {
    e.stopPropagation();
    onClick?.(e);
  };

  return (
    <IconButton
      onClick={handleClick}
      sx={{
        color: "text.primary",
        height: 24,
        width: 24,
        fontSize: 20,
      }}
      {...otherProps}
    >
      <EditIcon />
    </IconButton>
  );
};

export default memo(EditCell);
