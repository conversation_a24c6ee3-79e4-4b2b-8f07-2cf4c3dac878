"use client";

import http from "@/api";
import { ApiConstant, AppConstant, PathConstant } from "@/constant";
import { ILogin, ITokenLoginResponseModel, IUserLogin } from "../login.model";
import { DataResponseModel } from "@/models/response.model";
import { UseFormSetError } from "react-hook-form";
import { useAppDispatch } from "@/redux/hook";
import { appActions } from "@/redux/app.slice";
import Cookies from "js-cookie";
import { toast } from "sonner";
import { useRouter } from "next/navigation";

// Error Login Code
export const TENDANGNHAP_ERROR_CODE = "AEC_01";
export const MATKHAU_ERROR_CODE = "AEC_02";
export const KHOATAIKHOAN_ERROR_CODE = "AEC_03";

const useLogin = () => {
  const dispatch = useAppDispatch();
  const router = useRouter();

  const handleLogin = async ({
    data,
    setError,
    setIsSubmitting,
    saveCurrentInfo,
  }: {
    data: IUserLogin;
    setError: UseFormSetError<ILogin>;
    setIsSubmitting: (isSubmit: boolean) => void;
    saveCurrentInfo: () => void;
  }) => {
    try {
      setIsSubmitting(true);
      const res: DataResponseModel<ITokenLoginResponseModel> = await http.post(
        ApiConstant.LOGIN,
        data
      );
      if (res.code === ApiConstant.ERROR_CODE_OK) {
        const access_token = res.data?.access_token;
        const expired_time = res.data?.expired_time;

        const offsetInMinutes = new Date().getTimezoneOffset();

        const expiredDate = new Date(expired_time);

        expiredDate.setMinutes(expiredDate.getMinutes() - offsetInMinutes);

        Cookies.set(AppConstant.ACCESS_TOKEN, access_token, {
          expires: expiredDate,
        });

        Cookies.set(AppConstant.COOKIE_KEY.orgId, data.schoolId.toString(), {
          expires: expiredDate,
        });

        saveCurrentInfo();

        dispatch(
          appActions.getUserInfo({
            onSuccess: () => {
              router.push(PathConstant.ROOT);
            },
          })
        );
      } else if (
        res.code === TENDANGNHAP_ERROR_CODE ||
        res.code === KHOATAIKHOAN_ERROR_CODE
      ) {
        setError("username", { message: res.message });
      } else if (res.code === MATKHAU_ERROR_CODE) {
        setError("password", { message: res.message });
      }
    } catch (error: any) {
      toast.error("Thất bại!", {
        description:
          error?.data?.response?.message ||
          error?.message ||
          "Đã có lỗi xảy ra!",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return handleLogin;
};

export default useLogin;
