"use client";

import { TablePageLayout } from "@/components/common";
import React from "react";
import { IDeviceAdditionReport } from "../reportAdditionDevice.model";
import { ColumnDef } from "@tanstack/react-table";
import { formatNumber, formatPrice } from "@/utils/format.utils";
import {
  ActionType,
  FilterConfig,
} from "@/components/common/TablePageLayout/type";
import { ApiConstant } from "@/constant";
import FilterCustom from "./FilterCustom";

const DeviceAdditionReportPage = () => {
  return (
    <TablePageLayout<IDeviceAdditionReport>
      methodFetch="POST"
      excelConfig={{
        tableName: "Báo cáo tăng thiết bị",
        fileName: "bao_cao_tang_thiet_bi.xlsx",
      }}
      apiUrl="/api/statistic-report/device-increment"
      tableProps={{
        columns: COLUMN,
      }}
      visibleCol={VISIBLE_COL}
      filterConfig={FILTER_CONFIG}
      actions={ACTIONS}
      filterCustom={(props) => <FilterCustom props={props} />}
    />
  );
};
const ACTIONS: ActionType[] = ["exportExcel"];

export default DeviceAdditionReportPage;

const FILTER_CONFIG: FilterConfig[] = [
  {
    key: "fromDate",
  },
  {
    key: "toDate",
  },
  {
    key: "roomIds",
    type: "select",
    isMulti: true,
    apiListUrl: ApiConstant.ROOM,
    label: "Kho/Phòng",
    hasAllOption: true,
    isCollapse: true,
  },
  {
    key: "schoolBudgetCategoryIds",
    type: "select",
    isMulti: true,
    apiListUrl: ApiConstant.SOURCE,
    label: "Nguồn cấp",
    hasAllOption: true,
    isCollapse: true,
  },
];

const COLUMN: ColumnDef<IDeviceAdditionReport>[] = [
  {
    id: "deviceName",
    header: "Tên thiết bị",
    accessorKey: "deviceName",
    size: 150,
  },
  {
    id: "schoolDeviceTypeName",
    header: "Loại thiết bị",
    accessorKey: "schoolDeviceTypeName",
    size: 120,
  },
  {
    id: "deviceUnitName",
    header: "Đơn vị tính",
    accessorKey: "deviceUnitName",
    size: 80,
  },
  {
    id: "roomName",
    header: "Kho/Phòng",
    accessorKey: "roomName",
    size: 150,
  },
  {
    id: "quantity",
    header: "Số lượng",
    accessorKey: "quantity",
    accessorFn: (row) => formatNumber(row.quantity),
    size: 60,
    meta: {
      align: "right",
    },
  },
  {
    id: "price",
    header: "Đơn giá",
    accessorKey: "price",
    accessorFn: (row) => formatPrice(row.price),
    size: 150,
    meta: {
      align: "right",
    },
  },
  {
    id: "totalPrices",
    header: "Thành tiền",
    accessorKey: "totalPrices",
    accessorFn: (row) => formatPrice(row.totalPrices),
    size: 150,
    meta: {
      align: "right",
    },
  },
  {
    id: "schoolBudgetCategoryName",
    header: "Nguồn cấp",
    accessorKey: "schoolBudgetCategoryName",
    size: 150,
  },
];

const VISIBLE_COL = [
  { id: "deviceName", name: "Tên thiết bị" },
  { id: "schoolDeviceTypeName", name: "Loại thiết bị" },
  { id: "deviceUnitName", name: "Đơn vị tính" },
  { id: "roomName", name: "Kho/Phòng" },
  { id: "gradeName", name: "Khối lớp" },
  { id: "quantity", name: "Số lượng" },
  { id: "price", name: "Đơn giá" },
  { id: "totalPrices", name: "Thành tiền" },
  { id: "schoolBudgetCategoryName", name: "Nguồn cấp" },
];
