import useExportPDF from "@/hooks/useExportPDF";
import { Button } from "@mui/material";
import React, { JSX, memo } from "react";
import { useTableStore } from "../table-store/TableContext";
import { ColumnDef, VisibilityState } from "@tanstack/react-table";
import { FIXED_ID_ARR } from "../../table/AppTable";

const ExportPDFAction = <T,>({
  columnVisibility,
  configColumn,
}: ExportPDFActionProps<T>) => {
  const store = useTableStore<T>();
  const data = store((state) => state.data);
  const excelConfig = store((state) => state.excelConfig);
  const { exportToPDF, isExporting } = useExportPDF();

  const handleExport = () => {
    let columns = excelConfig?.columns ?? [];

    if (!columns?.length) {
      const hasVisibility = Object.keys(columnVisibility).length;

      let visibleColumns = configColumn;
      if (hasVisibility) {
        visibleColumns = filterColumnDefsByVisibility(
          configColumn,
          columnVisibility
        );
      }

      columns = transformToPDFColumns(visibleColumns as any, true); // hasIndexCol = true
    }

    exportToPDF({
      tableData: data as any,
      columns,
      title: excelConfig?.tableName || "Báo cáo",
      orientation: "portrait",
      onSuccess: () => {
        console.log("Xuất PDF thành công!");
      },
      onError: (error) => {
        console.error("Lỗi xuất PDF:", error);
      },
    });
  };

  return (
    <Button
      variant="outlined"
      color="secondary"
      onClick={handleExport}
      disabled={isExporting}
    >
      {isExporting ? "Đang xuất PDF..." : "Xuất PDF"}
    </Button>
  );
};

type ExportPDFActionProps<T> = {
  columnVisibility: VisibilityState;
  configColumn: ColumnDef<T>[];
};

export default memo(ExportPDFAction) as <T>(
  props: ExportPDFActionProps<T>
) => JSX.Element;

export type PDFColumnProps = {
  name: string; // Tương thích với ExcelColumnProps
  header?: string; // Giữ lại cho backward compatibility
  field?: string; // Có thể không có field cho group columns
  key?: string; // Tương thích với ExcelColumnProps
  width?: number;
  isGroup?: boolean;
  children?: PDFColumnProps[];
  colspan?: number;
  rowspan?: number;
  child?: PDFColumnProps[]; // Tương thích với ExcelColumnProps
};

export const transformToPDFColumns = (
  rawColumns: Array<ColumnDef<any, any> & { accessorKey?: string }>,
  hasIndexCol: boolean = true
): PDFColumnProps[] => {
  type Col = ColumnDef<any, any> & { accessorKey?: string };

  // Tính độ sâu tối đa của cây (giống Excel getMaxDepth)
  const getMaxDepth = (cols: Col[], depth = 1): number => {
    return Math.max(
      ...cols.map((col) =>
        "columns" in col && Array.isArray(col.columns)
          ? getMaxDepth(col.columns as Col[], depth + 1)
          : depth
      )
    );
  };

  const maxDepth = getMaxDepth(rawColumns);

  const buildColumn = (col: Col, depth = 1): PDFColumnProps => {
    let name: string = "";
    if (typeof col.header === "string") name = col.header;
    else if (
      typeof col.header === "function" ||
      React.isValidElement(col.header)
    )
      name = col.id ?? "";
    else if (col.header) name = String(col.header);
    else name = col.id ?? "";

    // Group column (giống Excel logic)
    if ("columns" in col && Array.isArray(col.columns)) {
      const children = (col.columns as Col[]).map((child) =>
        buildColumn(child, depth + 1)
      );

      // Tính colSpan giống Excel
      const colSpan = children.reduce(
        (sum, child) => sum + (child.colspan ?? 1),
        0
      );

      return {
        name,
        header: name,
        field: col.id ?? "",
        key: col.id,
        width: col.size,
        isGroup: true,
        children,
        child: children, // Tương thích với ExcelColumnProps
        colspan: colSpan,
        rowspan: 1, // Group column luôn có rowspan = 1
      };
    }

    // Leaf column (giống Excel logic)
    return {
      name,
      header: name,
      field: col.accessorKey ?? col.id ?? "",
      key: col.accessorKey ?? col.id,
      width: Math.round((col.size ?? 150) / 7), // Giống Excel width calculation
      isGroup: false,
      colspan: 1,
      rowspan: maxDepth - depth + 1, // Giống Excel: maxDepth - depth + 1
    };
  };

  const finalColumns: PDFColumnProps[] = hasIndexCol
    ? [
        {
          name: "STT",
          key: "index",
          field: "index",
          width: Math.round(50 / 7), // Giống Excel width calculation
          rowspan: maxDepth,
          colspan: 1,
        },
        ...rawColumns.map((col) => buildColumn(col)),
      ]
    : rawColumns.map((col) => buildColumn(col));

  return finalColumns;
};

export function filterColumnDefsByVisibility<T>(
  columns: ColumnDef<T>[],
  visibility: VisibilityState,
  hasVisibility = true
): ColumnDef<T>[] {
  return columns
    .map((col) => {
      const key = (col as any).accessorKey ?? col.id;

      // Bỏ cột nằm trong FIXED_ID_ARR
      if (FIXED_ID_ARR.includes(key)) return null;

      const isVisible = hasVisibility ? visibility[key] !== false : true;

      // Nếu là group column
      if ("columns" in col && Array.isArray(col.columns)) {
        const filteredChildren = filterColumnDefsByVisibility(
          col.columns,
          visibility,
          hasVisibility
        );

        // Bỏ luôn group nếu không còn con
        if (filteredChildren.length === 0) return null;

        return {
          ...col,
          columns: filteredChildren,
        };
      }

      return isVisible ? col : null;
    })
    .filter(Boolean) as ColumnDef<T>[];
}
