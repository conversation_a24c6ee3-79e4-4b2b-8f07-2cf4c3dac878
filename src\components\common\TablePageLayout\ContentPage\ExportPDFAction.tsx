import useExportPDF from "@/hooks/useExportPDF";
import { Button } from "@mui/material";
import React, { JSX, memo } from "react";
import { useTableStore } from "../table-store/TableContext";
import { ColumnDef, VisibilityState } from "@tanstack/react-table";
import { FIXED_ID_ARR } from "../../table/AppTable";

const ExportPDFAction = <T,>({
  columnVisibility,
  configColumn,
}: ExportPDFActionProps<T>) => {
  const store = useTableStore<T>();
  const data = store((state) => state.data);
  const excelConfig = store((state) => state.excelConfig);
  const { exportToPDF, isExporting } = useExportPDF();

  const handleExport = () => {
    let columns = excelConfig?.columns ?? [];

    if (!columns?.length) {
      const hasVisibility = Object.keys(columnVisibility).length;

      let visibleColumns = configColumn;
      if (hasVisibility) {
        visibleColumns = filterColumnDefsByVisibility(
          configColumn,
          columnVisibility
        );
      }

      columns = transformToPDFColumns(visibleColumns as any);
    }

    exportToPDF({
      tableData: data as any,
      columns,
      title: excelConfig?.tableName || "Báo cáo",
      orientation: "portrait",
      onSuccess: () => {
        console.log("Xuất PDF thành công!");
      },
      onError: (error) => {
        console.error("Lỗi xuất PDF:", error);
      },
    });
  };

  return (
    <Button
      variant="outlined"
      color="secondary"
      onClick={handleExport}
      disabled={isExporting}
    >
      {isExporting ? "Đang xuất PDF..." : "Xuất PDF"}
    </Button>
  );
};

type ExportPDFActionProps<T> = {
  columnVisibility: VisibilityState;
  configColumn: ColumnDef<T>[];
};

export default memo(ExportPDFAction) as <T>(
  props: ExportPDFActionProps<T>
) => JSX.Element;

export type PDFColumnProps = {
  name: string; // Tương thích với ExcelColumnProps
  header?: string; // Giữ lại cho backward compatibility
  field: string;
  width?: number;
  isGroup?: boolean;
  children?: PDFColumnProps[];
  colspan?: number;
  rowspan?: number;
  child?: PDFColumnProps[]; // Tương thích với ExcelColumnProps
};

export const transformToPDFColumns = (
  rawColumns: Array<ColumnDef<any, any> & { accessorKey?: string }>
): PDFColumnProps[] => {
  type Col = ColumnDef<any, any> & { accessorKey?: string };

  const buildColumn = (col: Col): PDFColumnProps => {
    let name: string = "";
    if (typeof col.header === "string") name = col.header;
    else if (
      typeof col.header === "function" ||
      React.isValidElement(col.header)
    )
      name = col.id ?? "";
    else if (col.header) name = String(col.header);
    else name = col.id ?? "";

    // Group column
    if ("columns" in col && Array.isArray(col.columns)) {
      const children = (col.columns as Col[]).map((child) =>
        buildColumn(child)
      );
      // Tính tổng số cột con (không phải tổng colspan)
      const totalChildren = children.reduce((sum, child) => {
        if (child.isGroup) {
          return sum + (child.colspan || 1);
        } else {
          return sum + 1;
        }
      }, 0);

      return {
        name,
        header: name,
        field: col.id ?? "",
        width: col.size,
        isGroup: true,
        children,
        child: children, // Tương thích với ExcelColumnProps
        colspan: totalChildren,
        rowspan: 1,
      };
    }

    // Leaf column
    return {
      name,
      header: name,
      field: col.accessorKey ?? col.id ?? "",
      width: col.size,
      isGroup: false,
      colspan: 1,
      rowspan: 2, // 2 rows: 1 cho header group, 1 cho header leaf
    };
  };

  return rawColumns.map((col) => buildColumn(col));
};

export function filterColumnDefsByVisibility<T>(
  columns: ColumnDef<T>[],
  visibility: VisibilityState,
  hasVisibility = true
): ColumnDef<T>[] {
  return columns
    .map((col) => {
      const key = (col as any).accessorKey ?? col.id;

      // Bỏ cột nằm trong FIXED_ID_ARR
      if (FIXED_ID_ARR.includes(key)) return null;

      const isVisible = hasVisibility ? visibility[key] !== false : true;

      // Nếu là group column
      if ("columns" in col && Array.isArray(col.columns)) {
        const filteredChildren = filterColumnDefsByVisibility(
          col.columns,
          visibility,
          hasVisibility
        );

        // Bỏ luôn group nếu không còn con
        if (filteredChildren.length === 0) return null;

        return {
          ...col,
          columns: filteredChildren,
        };
      }

      return isVisible ? col : null;
    })
    .filter(Boolean) as ColumnDef<T>[];
}
