export interface IReportDevice {
  id: number;

  /** Mã */
  code: string;

  /** Id chứng từ */
  deviceTransactionId: number;

  /** Id thiết bị */
  deviceDefinitionId: number;

  /** Id phòng */
  roomId?: number;

  /** Tên phòng */
  roomName?: string;

  /** Id giáo viên phụ trách */
  teacherId?: number;

  /** Tên giáo viên phụ trách */
  teacherName?: string;

  /** Số lượng */
  quantity: number;

  /** Giá tiền */
  price: number;

  /** Tổng giá tiền (tính từ quantity * price) */
  totalPrices: number;

  /** Quốc gia */
  countryId?: number;

  /** Tên Quốc gia */
  countryName?: string;

  /** Ngày nhập */
  entryDate?: Date;

  /** Số Seri */
  serial?: string;

  /** <PERSON><PERSON><PERSON> hết hạn */
  expireDate?: Date;

  /** <PERSON><PERSON> lượng hỏng */
  totalBroken?: number;

  /** <PERSON><PERSON> lượng mất */
  totalLost?: number;

  /** <PERSON><PERSON> lượng còn sử dụng */
  totalAvailable?: number;

  /** Số lượng đang mượn */
  totalBorrowing?: number;

  /** Số lượng đang đăng ký */
  totalRegister?: number;

  /** Số lượng sẵn sàng cho mượn (totalAvailable - totalBorrowing) */
  totalBorrowReady: number;

  // --- Thông tin thiết bị ---
  /** Mã thống kê */
  statisticCode: string;

  /** Mã thiết bị */
  deviceCode: string;

  /** Tên thiết bị */
  deviceName: string;

  /** Đơn vị tính */
  deviceUnitId: number;

  /** Tên Đơn vị tính */
  deviceUnitName?: string;

  /** Loại thiết bị */
  schoolDeviceTypeId?: number;

  /** Tên loại thiết bị */
  schoolDeviceTypeName?: string;

  /** Loại thiết bị theo DTI */
  deviceDTITypeId?: number;

  /** Tên loại thiết bị theo DTI */
  deviceDTITypeName?: string;

  /** Môn học */
  schoolSubjectId?: number;

  /** Tên môn học */
  schoolSubjectName?: string;

  /** Danh sách khối */
  gradeCodes: number[];

  /** Khối lớp */
  gradeCode?: string;

  /** Tên khối/lớp */
  gradeName?: string;

  /** Đối tượng sử dụng */
  userType?: string;

  /** Danh sách đối tượng sử dụng: 1 => giáo viên, 2 => học sinh */
  userTypes: number[];

  // --- Thông tin chứng từ ---
  /** Số phiếu */
  documentNumber: string;

  /** Ngày nhập */
  documentDate: Date;

  deviceTransactionItemId: number;

  /** Số lượng hỏng trên chứng từ */
  transactionTotalBroken?: number;

  /** Số lượng mất trên chứng từ */
  transactionTotalLost?: number;

  /** Số lượng còn sử dụng trên chứng từ */
  transactionTotalAvailable?: number;
}

export enum StatisticDeviceStatusEnum {
  lost = 0, // Mất
  available = 1, // Còn sử dụng
  broken = 2, // Hỏng
}

export enum StatisticDeviceStatusEnum {
  Lost = 0,
  Available = 1,
  Broken = 2,
}

export const StatisticDeviceStatusList: {
  id: StatisticDeviceStatusEnum;
  label: string;
}[] = [
  { id: StatisticDeviceStatusEnum.Lost, label: "Mất" },
  { id: StatisticDeviceStatusEnum.Available, label: "Còn sử dụng" },
  { id: StatisticDeviceStatusEnum.Broken, label: "Hỏng" },
];
