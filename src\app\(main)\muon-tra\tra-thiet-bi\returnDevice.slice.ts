import { rootReducer } from "@/redux/reducer";
import { RootState } from "@/redux/store";
import {
  PayloadAction,
  WithSlice,
  createSelector,
  createSlice,
} from "@reduxjs/toolkit";
import {
  EditedDataRecord,
  IEditedDeviceData,
  ReturnDeviceFieldName,
} from "./returnDevice.model";

/* ------------- Initial State ------------- */
export interface IInitialState {
  editedData: EditedDataRecord;
  headerDate: string | null;
}

const initialState: IInitialState = {
  editedData: {},
  headerDate: null,
};

/* ------------- Selector ------------- */
export const selectEditedRows = createSelector(
  [(state: RootState) => state.returnDeviceReducer?.editedData ?? {}],
  (editedData) => editedData
);

export const selectHeaderDate = createSelector(
  [(state: RootState) => state.returnDeviceReducer?.editedData ?? {}],
  (editedData) => {
    const editedDates = Object.values(editedData)
      .map((data) => data.borrowReturnDate)
      .filter((date) => date !== undefined);

    if (editedDates.length === 0) return null;

    const firstDate = editedDates[0];
    const allSameDate = editedDates.every((date) => date === firstDate);

    return allSameDate ? firstDate : null;
  }
);

// Memoized selectors với createSelector cho từng row
export const makeSelectEditedDataById = () =>
  createSelector(
    [returnDeviceSelectors.selectEditedData, (_: any, rowId: number) => rowId],
    (editedData, rowId) => editedData[rowId] ?? EMPTY_DEVICE_DATA
  );

export const makeSelectTotalBrokenById = () =>
  createSelector(
    [returnDeviceSelectors.selectEditedData, (_: any, rowId: number) => rowId],
    (editedData, rowId) => editedData[rowId]?.totalBroken
  );

export const makeSelectTotalLostById = () =>
  createSelector(
    [returnDeviceSelectors.selectEditedData, (_: any, rowId: number) => rowId],
    (editedData, rowId) => editedData[rowId]?.totalLost
  );

export const makeSelectTotalConsumedById = () =>
  createSelector(
    [returnDeviceSelectors.selectEditedData, (_: any, rowId: number) => rowId],
    (editedData, rowId) => editedData[rowId]?.totalConsumed
  );

export const makeSelectNotesById = () =>
  createSelector(
    [returnDeviceSelectors.selectEditedData, (_: any, rowId: number) => rowId],
    (editedData, rowId) => editedData[rowId]?.notes
  );

export const makeSelectBorrowReturnDateById = () =>
  createSelector(
    [returnDeviceSelectors.selectEditedData, (_: any, rowId: number) => rowId],
    (editedData, rowId) => editedData[rowId]?.borrowReturnDate
  );

/* ------------- Reducers ------------- */
const reducers = {
  updateEditedValue: (
    state: IInitialState,
    action: PayloadAction<{
      rowId: number;
      field: ReturnDeviceFieldName;
      value: number | string;
    }>
  ) => {
    const { rowId, field, value } = action.payload;
    if (!state.editedData[rowId]) {
      state.editedData[rowId] = {};
    }
    (state.editedData[rowId] as any)[field] = value;
  },
  resetEditedData: (state: IInitialState) => {
    state.editedData = {};
  },
  setEditedData: (
    state: IInitialState,
    action: PayloadAction<EditedDataRecord>
  ) => {
    state.editedData = action.payload;
  },
  setHeaderDate: (
    state: IInitialState,
    action: PayloadAction<string | null>
  ) => {
    state.headerDate = action.payload;
  },
  updateHeaderDateForAllRows: (
    state: IInitialState,
    action: PayloadAction<string>
  ) => {
    const headerDate = action.payload;
    // Lấy tất cả rowIds từ editedData hiện có
    const allRowIds = Object.keys(state.editedData).map((id) => parseInt(id));

    allRowIds.forEach((rowId) => {
      if (!state.editedData[rowId]) {
        state.editedData[rowId] = {};
      }
      state.editedData[rowId].borrowReturnDate = headerDate;
    });
  },
  setDefaultDateForAllRows: (
    state: IInitialState,
    action: PayloadAction<{
      rowIds: number[];
      defaultDate: string;
    }>
  ) => {
    const { rowIds, defaultDate } = action.payload;
    rowIds.forEach((rowId) => {
      if (!state.editedData[rowId]) {
        state.editedData[rowId] = {};
      }
      state.editedData[rowId].borrowReturnDate = defaultDate;
    });
  },
};

export const returnDeviceSlice = createSlice({
  name: "returnDeviceReducer",
  initialState,
  reducers,
  selectors: {
    selectReturnDeviceState: (state) => state,
    selectEditedData: (state) => state.editedData,
    selectHeaderDate: (state) => state.headerDate,
  },
});

export const returnDeviceActions = returnDeviceSlice.actions;

// ============================= INJECT REDUCER =============================
// Extend LazyLoadedSlices để có thể inject động
declare module "@/redux/reducer" {
  export interface LazyLoadedSlices
    extends WithSlice<typeof returnDeviceSlice> {}
}

// Inject reducer
const injectedReturnDeviceSlice = returnDeviceSlice.injectInto(rootReducer);

export const returnDeviceSelectors = injectedReturnDeviceSlice.selectors;

// Stable empty objects để tránh tạo reference mới
const EMPTY_DEVICE_DATA: IEditedDeviceData = {};

// ============================= EXPORT DEFAULT =============================
export default returnDeviceSlice.reducer;
