import { create, StateCreator } from "zustand";
import { AppConstant, ApiConstant, EnvConstant } from "@/constant";
import http from "@/api";
import { toast } from "sonner";
import { CANCEL_MSG } from "@/constant/api.const";
import { buildFilterParamsFromConfig } from "../helper";
import {
  DataListResponseModel,
  IPaginationModel,
} from "@/models/response.model";
import { FilterConfig, FilterValueType } from "../type";

export interface TableStoreState<T> {
  apiUrl?: string;
  onResetFilter?: (filter: FilterConfig[] | null) => void;
  methodFetch?: "GET" | "POST";
  data: T[];
  isLoading: boolean;
  error: string | null;
  totalCount: number;
  pagination: IPaginationModel;
  filter: FilterConfig[] | null;
  filterOptions: Record<string, any[]>;
  isLoadingFilter: boolean;

  filterConfig: FilterConfig[] | null;
  setFilterConfig: (arr: FilterConfig[] | null) => void;

  formatData?: (data: T[]) => T[];
  fetchAll?: boolean;
  cleanDataFormFiled?: (data: T) => any;
  abortController?: AbortController;
  setAbortController: (controller?: AbortController) => void;

  excelConfig?: Partial<ExportExcelProps>;

  setPagination: (pagination: IPaginationModel) => void;
  setFilter: (filter: FilterConfig[] | null) => void;
  setFilterOptions: (options: Record<string, any[]>) => void;
  handleChangeFilter: (key: string) => (value: FilterValueType) => void;
  handleChangeFilterObj: (filter: Record<string, FilterValueType>) => void;
  handleClearFilter: () => void;
  createFetcher: (config: {
    apiUrl?: string;
    formatData?: (data: T[], filter?: Record<string, any>) => T[];
    fetchAll?: boolean;
    methodFetch?: "GET" | "POST";
  }) => (params: {
    pagination: IPaginationModel;
    filter: Record<string, any>;
  }) => Promise<void>;
  fetchCurrentData: () => (() => void) | void;
  reset: () => void;
}

import { subscribeWithSelector } from "zustand/middleware";
import { ExportExcelProps } from "@/models/types";

export const createTableStore = <T>(initialState?: {
  apiUrl?: string;
  methodFetch?: "GET" | "POST";
  cleanDataFormFiled?: (data: T) => any;
  formatData?: (data: T[]) => T[];
  fetchAll?: boolean;
  excelConfig?: Partial<ExportExcelProps>;
  onResetFilter?: (filter: FilterConfig[] | null) => void;
}) => {
  let latestRequestId = 0;

  const storeCreator: StateCreator<
    TableStoreState<T>,
    [["zustand/subscribeWithSelector", never]]
  > = (set, get) => ({
    apiUrl: initialState?.apiUrl || "",
    methodFetch: initialState?.methodFetch || "GET",
    cleanDataFormFiled: initialState?.cleanDataFormFiled,
    onResetFilter: initialState?.onResetFilter,
    formatData: initialState?.formatData,
    fetchAll: initialState?.fetchAll ?? false,
    isLoadingFilter: false,

    filterConfig: [],
    setFilterConfig: (arr) => {
      set({
        filterConfig: arr,
      });
    },

    excelConfig: initialState?.excelConfig,

    data: [],
    isLoading: false,
    error: null,
    totalCount: 0,
    pagination: AppConstant.DEFAULT_PAGINATION_SKIP_TAKE,
    filter: null,
    filterOptions: {},

    abortController: undefined,
    setAbortController: (abort) => set({ abortController: abort }),

    setPagination: (pagination) => set({ pagination }),
    setFilter: (filter) => set({ filter }),
    setFilterOptions: (options) => {
      const { filterOptions } = get();

      set({ filterOptions: { ...filterOptions, ...options } });
    },

    handleChangeFilter: (key) => (value) => {
      const { filterConfig } = get();
      const filterConfigItem = filterConfig?.find((f) => f.key === key);
      if (filterConfigItem?.onChangeValue) {
        filterConfigItem.onChangeValue(value);
      }
      set((state) => ({
        filter:
          state.filter?.map((f) => (f.key === key ? { ...f, value } : f)) ||
          null,
        pagination: AppConstant.DEFAULT_PAGINATION_SKIP_TAKE,
      }));
    },
    handleChangeFilterObj: (newFilter: Record<string, FilterValueType>) => {
      const { filterConfig } = get();

      const keyFilter = Object.keys(newFilter);
      keyFilter.forEach((key) => {
        const filterConfigItem = filterConfig?.find((f) => f.key === key);

        if (filterConfigItem?.onChangeValue) {
          filterConfigItem.onChangeValue(newFilter[key]);
        }
      });
      set((state) => ({
        filter: state.filter
          ? state.filter.map((f) =>
              newFilter.hasOwnProperty(f.key)
                ? { ...f, value: newFilter[f.key] }
                : f
            )
          : null,
        pagination: AppConstant.DEFAULT_PAGINATION_SKIP_TAKE,
      }));
    },

    handleClearFilter: () => {
      const { filterConfig, onResetFilter } = get();

      onResetFilter?.(filterConfig);

      set({
        filter: filterConfig,
        pagination: AppConstant.DEFAULT_PAGINATION_SKIP_TAKE,
      });
    },

    createFetcher:
      ({ apiUrl, formatData, fetchAll = false, methodFetch = "GET" }) =>
      async ({ pagination, filter }) => {
        const currentRequestId = ++latestRequestId;
        set({ isLoading: true, error: null });

        try {
          const { abortController } = get();

          const params = {
            ...(fetchAll ? {} : { ...pagination }),
            ...filter,
          };

          if (!apiUrl) throw new Error("Failed to fetch data");

          let response: DataListResponseModel<T>;

          if (methodFetch === "POST") {
            response = await http.post<DataListResponseModel<T>>(
              apiUrl,
              params,
              { controller: abortController }
            );
          } else {
            response = await http.get<DataListResponseModel<T>>(apiUrl, {
              params,
              controller: abortController,
            });
          }

          if (currentRequestId !== latestRequestId) return;

          if (response.code === ApiConstant.ERROR_CODE_OK) {
            const rawData = response.data.data;
            const data = formatData ? formatData(rawData, filter) : rawData;
            set({
              data,
              totalCount: response.data.totalCount,
            });
          } else {
            throw new Error("Failed to fetch data");
          }
        } catch (err: any) {
          EnvConstant.IS_DEV && console.log(err);
          if (err?.name === "AbortError" || err?.message === CANCEL_MSG) {
            console.warn("🛑 Request bị hủy:", apiUrl);
            return;
          }

          set({ error: err?.message || "Đã xảy ra lỗi không xác định" });

          toast.error("Thất bại!", {
            description:
              typeof err?.payload === "string"
                ? err.payload
                : JSON.stringify(err?.payload) || "Lỗi khi lấy dữ liệu",
          });
        } finally {
          if (currentRequestId === latestRequestId) {
            set({ isLoading: false });
          }
        }
      },

    fetchCurrentData: () => {
      const state = get();

      if (!state.apiUrl) return;

      if (
        state.filter?.some(
          (f) =>
            f.required &&
            (f.value === null ||
              f.value === undefined ||
              f.value.toString().trim() === "" ||
              (Array.isArray(f.value) && f.value.length === 0))
        )
      ) {
        return;
      }

      const filterParams = buildFilterParamsFromConfig(state.filter);

      const controller = new AbortController();
      state.setAbortController(controller);

      state.createFetcher({
        apiUrl: state.apiUrl,
        formatData: state.formatData,
        fetchAll: state.fetchAll,
        methodFetch: state.methodFetch,
      })({
        pagination: state.pagination,
        filter: filterParams,
      });

      return () => {
        controller.abort();
        state.setAbortController(undefined);
      };
    },

    reset: () =>
      set({
        data: [],
        isLoading: false,
        error: null,
        totalCount: 0,
        pagination: AppConstant.DEFAULT_PAGINATION_SKIP_TAKE,
        filter: null,
        filterOptions: {},
        apiUrl: initialState?.apiUrl,
        filterConfig: [],
      }),
  });

  return create(subscribeWithSelector(storeCreator));
};
