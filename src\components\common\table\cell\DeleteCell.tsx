import { DeleteIcon } from "@/components/icons";
import { IconButton, IconButtonProps } from "@mui/material";
import { memo } from "react";

const DeleteCell = ({ sx, onClick, ...otherProps }: DeleteCellProps) => {
  const handleClick = (e) => {
    e.stopPropagation();
    onClick?.(e);
  };
  return (
    <IconButton
      onClick={handleClick}
      id="delete"
      sx={{
        width: 24,
        height: 24,
        fontSize: 20,
        ...sx,
      }}
      {...otherProps}
    >
      <DeleteIcon />
    </IconButton>
  );
};

type DeleteCellProps = IconButtonProps;

export default memo(DeleteCell);
