export interface ITransferDevice {
  deviceId: number;
  isError?: boolean;
  toRoomId?: number;
  totalBroken?: number;
  totalLost?: number;
  totalAvailable?: number;
}

export interface IDeviceTransfer {
  id: number;
  createdBy: number;
  updatedBy: number;
  createdAt: string;
  updatedAt: string;
  deviceDefinitionId: number;
  deviceName: string;
  isManageQuantity: number;
  fromDeviceId: number;
  toDeviceId: number;
  transferDate: string;
  fromRoomId: number;
  toRoomId: number;
  fromRoomName: string;
  toRoomName: string;
  totalBroken: number;
  totalLost: number;
  totalAvailable: number;
  totalTransfer: number;
  status: number;
}

export interface IDeviceTransferParams {
  deviceDefinitionIds?: number[];
  deviceIds?: number[];
  searchKey?: string;
}

export enum MINIMUM_QUANTITY {
  /// <summary>
  /// Thiếu
  /// </summary>
  Deficient = 0,

  /// <summary>
  /// Đủ
  /// </summary>
  Sufficient = 1,

  /// <summary>
  /// Thừa
  /// </summary>
  Surplus = 2,
}

// Lịch sử thiết bị
export interface IHistoryDevice {
  id: number;
  deviceCode: string;
  deviceName: string;
  deviceUnitId: number;
  deviceUnitName: string;
  totalBorrowReady: number;
  totalBorrowing: number;
  totalRegister: number;
  transactionHistories: ITransactionHistory[];
  borrowHistories: IBorrowHistory[];
}

export interface ITransactionHistory {
  id: number | string;
  deviceTransactionId: number;
  documentDate: string;
  transactionType: number;
  transactionTypeName: string;
  documentNumber: string;
  quantity: number;
  roomId: number;
  roomName: string;
  notes: string;
}

export interface IBorrowHistory {
  id: number | string;
  teacherId: number;
  teacherName: string;
  quantity: number;
  borrowFromDate: string;
  borrowToDate: string;
  borrowStatus: number;
  borrowStatusName: string;
}
