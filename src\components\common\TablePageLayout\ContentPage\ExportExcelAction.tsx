import useHandleExcel from "@/hooks/useHandleExcel";
import { Button } from "@mui/material";
import React, { JSX, memo } from "react";
import { useTableStore } from "../table-store/TableContext";
import { ColumnDef, VisibilityState } from "@tanstack/react-table";

const ExportExcelAction = <T,>({
  columnVisibility,
  configColumn,
}: ExportExcelActionProps<T>) => {
  const store = useTableStore<T>();
  const data = store((state) => state.data);
  const excelConfig = store((state) => state.excelConfig);
  const { handleExportExcel } = useHandleExcel();

  const handleExport = () => {
    let columns = excelConfig?.columns ?? [];

    if (!columns?.length) {
      const hasVisibility = Object.keys(columnVisibility).length;

      let visibleColumns = configColumn;
      if (hasVisibility) {
        visibleColumns = filterColumnDefsByVisibility(
          configColumn,
          columnVisibility
        );
      }

      columns = transformToExcelColumns(visibleColumns as any);
    }

    handleExportExcel({
      columns,
      ...excelConfig,
      rows: data as any,
    });
  };

  return (
    <Button variant="outlined" color="secondary" onClick={handleExport}>
      Xuất Excel
    </Button>
  );
};

type ExportExcelActionProps<T> = {
  columnVisibility: VisibilityState;
  configColumn: ColumnDef<T>[];
};

export default memo(ExportExcelAction) as <T>(
  props: ExportExcelActionProps<T>
) => JSX.Element;

import { Style } from "exceljs";
import { FIXED_ID_ARR } from "../../table/AppTable";

export type ExcelColumnProps = {
  style?: Partial<Style>;
  name: string;
  key?: string;
  width?: number;
  rowSpan?: number;
  colSpan?: number;
  child?: ExcelColumnProps[];
};

export const transformToExcelColumns = (
  rawColumns: Array<ColumnDef<any, any> & { accessorKey?: string }>
): ExcelColumnProps[] => {
  type Col = ColumnDef<any, any> & { accessorKey?: string };

  // Tính độ sâu tối đa của cây
  const getMaxDepth = (cols: Col[], depth = 1): number => {
    return Math.max(
      ...cols.map((col) =>
        "columns" in col && Array.isArray(col.columns)
          ? getMaxDepth(col.columns as Col[], depth + 1)
          : depth
      )
    );
  };

  const maxDepth = getMaxDepth(rawColumns);

  const buildColumn = (col: Col, depth = 1): ExcelColumnProps => {
    const style: Partial<Style> = {
      alignment: col.meta?.align
        ? { horizontal: col.meta.align }
        : ("left" as any),
    };

    let name: string = "";
    if (typeof col.header === "string") name = col.header;
    else if (
      typeof col.header === "function" ||
      React.isValidElement(col.header)
    )
      name = col.id ?? "";
    else if (col.header) name = String(col.header);
    else name = col.id ?? "";

    // Group column
    if ("columns" in col && Array.isArray(col.columns)) {
      const children = (col.columns as Col[]).map((child) =>
        buildColumn(child, depth + 1)
      );
      const colSpan = children.reduce(
        (sum, child) => sum + (child.colSpan ?? 1),
        0
      );

      return {
        name,
        key: col.id,
        style,
        colSpan,
        rowSpan: 1,
        child: children,
      };
    }

    // Leaf column
    return {
      name,
      key: col.accessorKey ?? col.id,
      width: Math.round((col.size ?? 150) / 7),
      style,
      colSpan: 1,
      rowSpan: maxDepth - depth + 1,
    };
  };

  return rawColumns.map((col) => buildColumn(col));
};

export function filterColumnDefsByVisibility<T>(
  columns: ColumnDef<T>[],
  visibility: VisibilityState,
  hasVisibility = true
): ColumnDef<T>[] {
  return columns
    .map((col) => {
      const key = (col as any).accessorKey ?? col.id;

      // Bỏ cột nằm trong FIXED_ID_ARR
      if (FIXED_ID_ARR.includes(key)) return null;

      const isVisible = hasVisibility ? visibility[key] !== false : true;

      // Nếu là group column
      if ("columns" in col && Array.isArray(col.columns)) {
        const filteredChildren = filterColumnDefsByVisibility(
          col.columns,
          visibility,
          hasVisibility
        );

        // Bỏ luôn group nếu không còn con
        if (filteredChildren.length === 0) return null;

        return {
          ...col,
          columns: filteredChildren,
        };
      }

      return isVisible ? col : null;
    })
    .filter(Boolean) as ColumnDef<T>[];
}
