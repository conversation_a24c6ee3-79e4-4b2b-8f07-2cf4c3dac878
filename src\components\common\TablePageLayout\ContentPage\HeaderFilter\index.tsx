"use client";

import { <PERSON>, <PERSON><PERSON>, <PERSON>lapse, Di<PERSON><PERSON>, <PERSON>rid, Toolt<PERSON> } from "@mui/material";
import dynamic from "next/dynamic";
import React, {
  memo,
  useMemo,
  forwardRef,
  JSX,
  ReactNode,
  useState,
} from "react";
import ListFilter from "./ListFilter";
import { ActionType, FilterConfig, FilterValueType } from "../../type";
import {
  ClearFilterIcon,
  FilterActionsIcon,
  ReloadIcon,
} from "@/components/icons";
import { VisibilityState } from "@tanstack/react-table";
import { IColumnVisible } from "./ColumnToggleButton/useColumnToggle";
import IconButtonCustom from "./IconButtonCustom";
import { useTableStore } from "../../table-store/TableContext";
import { useModalAction } from "../../modal-store/useModalAction";

const AdvancedFilter = dynamic(() => import("./AdvancedFilter"), {
  ssr: false,
});

const ColumnToggleButton = dynamic(() => import("./ColumnToggleButton"), {
  ssr: false,
});

const HeaderFilter = <T,>(
  props: HeaderFilterProps<T>,
  ref: React.Ref<HTMLDivElement>
) => {
  const {
    actions,
    customActions,
    visibleCol,
    hasReload = true,
    setColumnVisibility,
    filterCustom,
    collapseFilterCustom,
    ExportExcelAction,
    ExportPDFAction,
  } = props;

  const store = useTableStore<T>();

  const filter = store((state) => state.filter);
  const handleClearFilter = store((state) => state.handleClearFilter);
  const fetchCurrentData = store((state) => state.fetchCurrentData);
  const handleChangeFilter = store((state) => state.handleChangeFilter);
  const handleChangeFilterObj = store((state) => state.handleChangeFilterObj);

  const [isOpen, setIsOpen] = useState(true);

  const { openCreateModal } = useModalAction();

  const hasAdvanced = useMemo(
    () => filter?.some((item) => Boolean(item.isAdvanced)),
    [filter]
  );

  const { filterShow, filterCollapse } = useMemo(() => {
    const show: typeof filter = [];
    const collapse: typeof filter = [];

    filter?.forEach((item) => {
      if (!item.isAdvanced && item.type) {
        if (item.isCollapse) {
          collapse.push(item);
        } else {
          show.push(item);
        }
      }
    });

    return {
      filterShow: show,
      filterCollapse: collapse,
    };
  }, [filter]);

  const hasCollapse = Boolean(filterCollapse.length) || collapseFilterCustom;
  const hasFilter = Boolean(filterShow.length) || filterCustom;

  return (
    <Box ref={ref}>
      <Grid
        px={3}
        container
        columnSpacing={SPACING_COLUMN_FILTER}
        rowSpacing={SPACING_ROW_FILTER}
        py={1}
      >
        {hasFilter && (
          <>
            <ListFilter filter={filterShow} />
            {filterCustom?.({
              filter,
              onChangeFilter: handleChangeFilter,
              onChangeFilterObj: handleChangeFilterObj,
            })}
          </>
        )}
        <Grid flex={1} gap={1} container>
          {hasAdvanced && <AdvancedFilter />}
          {hasCollapse && (
            <Tooltip title={isOpen ? "Ẩn bộ lọc" : "Hiện bộ lọc"} arrow>
              <IconButtonCustom
                aria-label="Ẩn/hiện bộ lọc"
                onClick={() => setIsOpen(!isOpen)}
              >
                <FilterActionsIcon />
              </IconButtonCustom>
            </Tooltip>
          )}
          {(hasCollapse || hasFilter) && (
            <Tooltip title="Xóa bộ lọc" arrow>
              <IconButtonCustom
                aria-label="Xóa bộ lọc"
                onClick={handleClearFilter}
              >
                <ClearFilterIcon />
              </IconButtonCustom>
            </Tooltip>
          )}
        </Grid>
        <Grid justifyContent="flex-end" gap={1} container>
          {visibleCol && (
            <ColumnToggleButton
              idKey="table"
              columns={visibleCol}
              onChangeColumnVisibility={setColumnVisibility}
            />
          )}
          {hasReload && (
            <Tooltip arrow title="Tải lại dữ liệu">
              <IconButtonCustom
                aria-label="Khởi tạo dữ liệu"
                onClick={fetchCurrentData}
              >
                <ReloadIcon />
              </IconButtonCustom>
            </Tooltip>
          )}
          {ExportExcelAction}
          {ExportPDFAction}
          {customActions}
          {actions?.includes("create") && (
            <Button variant="contained" onClick={openCreateModal}>
              Thêm mới
            </Button>
          )}
        </Grid>
      </Grid>
      {hasCollapse && (
        <>
          <Divider />
          <Collapse in={isOpen}>
            <Grid
              px={3}
              container
              columnSpacing={SPACING_COLUMN_FILTER}
              rowSpacing={SPACING_ROW_FILTER}
              py={1}
              minHeight={52}
            >
              <>
                <ListFilter filter={filterCollapse} />
              </>
              {collapseFilterCustom?.({
                filter,
                onChangeFilter: handleChangeFilter,
                onChangeFilterObj: handleChangeFilterObj,
              })}
            </Grid>
          </Collapse>
        </>
      )}
    </Box>
  );
};

export const SPACING_ROW_FILTER = 2;
export const SPACING_COLUMN_FILTER = 1.5;
export const DEFAULT_FILTER_SIZE = 2.4;

export interface FilterCustomProps {
  filter: FilterConfig[] | null;
  onChangeFilter: (key: string) => (value: FilterValueType) => void;
  onChangeFilterObj?: (filter: Record<string, FilterValueType>) => void;
}

export type HeaderFilterProps<T> = {
  actions?: ActionType[];
  /** Custom các nút header */
  customActions?: ReactNode;

  hasReload?: boolean;

  ExportExcelAction?: ReactNode;
  ExportPDFAction?: ReactNode;

  /** Cấu hình ẩn hiện cột */
  visibleCol?: Array<IColumnVisible>;
  setColumnVisibility: (state: VisibilityState) => void;

  /** Custom filter */
  filterCustom?: (props: FilterCustomProps) => ReactNode;
  /** Custom filter Collapse*/
  collapseFilterCustom?: (props: FilterCustomProps) => ReactNode;
};

export default memo(forwardRef(HeaderFilter)) as <T>(
  props: HeaderFilterProps<T> & { ref?: React.Ref<HTMLDivElement> }
) => JSX.Element;
