// Common
export const HEADER_DEFAULT = {
  Accept: "application/json",
  "Content-Type": "application/json",
};

export const TIMEOUT = 30000;

// HTTP Status
export const STT_OK = 200;
export const STT_CREATED = 201;
export const STT_BAD_REQUEST = 400;
export const STT_UNAUTHORIZED = 401;
export const STT_FORBIDDEN = 403;
export const STT_NOT_FOUND = 404;
export const STT_TIMEOUT = 408;
export const STT_INTERNAL_SERVER = 500;
export const STT_NOT_MODIFIED = 304;

export const CANCEL_MSG = "Request was cancelled";

// Error code
export const ERROR_CODE_OK = "1";

export const UPLOAD_IMG = "/api/v1/media/vlib/images";
export const UPLOAD_FILE = "/api/v1/media/vlib/files";

// app
export const GET_DOMAIN_INFO = "/get-domain-info";
export const GET_USER_INFO = "/v1/user/get-current-user-info";

// menu side bar
export const GET_MENU_SIDE_BAR = "/v1/school-menu-config/tree/{id}";

// system
export const GET_GRADE = "/v1/master-data/grade/list";
export const COUNTRY = "/v1/master-data/country/list";

// Class
export const CLASS = "/v1/school-class";
export const SYNC_CLASS = "/v1/sync-moet-data/class";

// Subject
export const SUBJECT = "/v1/school-subject";
export const STATUS_SUBJECT = "/v1/school-subject/change-status/{id}/{status}";
export const SYNC_SUBJECT = "/v1/school-subject/sync-data";

// Device type
export const DEVICE_TYPE = "/v1/school-device-type";
export const STATUS_DEVICE_TYPE =
  "/v1/school-device-type/change-status/{id}/{status}";
export const SYNC_DEVICE_TYPE = "/v1/school-device-type/sync-data";

// Device Type DTI
export const DEVICE_TYPE_DTI = "/v1/master-data/device-dti-type/list";

// Source
export const SOURCE = "/v1/school-budget-category";
export const STATUS_SOURCE =
  "/v1/school-budget-category/change-status/{id}/{status}";
export const SYNC_SOURCE = "/v1/school-budget-category/sync-data";

// master data
export const GET_DOET_LIST = "/v1/master-data/doet/list";
export const GET_DIVISION_LIST = "/v1/master-data/division/list";
export const GET_DON_VI = "/v1/master-data/school/list"; //api cho sở, phòng, trường
export const DEVICE_UNIT = "/v1/master-data/device-unit/list";
export const GET_SCHOOL_LEVEL_LIST = "/v1/master-data/school-level/list";
export const GET_SCHOOL_YEAR_LIST = "/v1/master-data/school-year/list";

// login
export const LOGIN = "/v1/user/login-school";
export const CHANGE_PASSWORD = "/v1/user/change-password";
export const SCHOOL_YEAR_CHANGE = "/v1/user/change-school-year";

export const MENU_CONFIG = "/v1/menu-config/tree";
export const MENU_TYPE = "/v1/menu-type";
export const DELETE_MENU = "/v1/menu-config/{id}";
export const ADD_MENU = "/v1/menu-config";
export const UPDATE_MENU = "/v1/menu-config/{id}";
export const MENU_COMBO = "/v1/menu-config/combo/{menuTypeId}";

// Room
export const ROOM = "/v1/room";
export const STATUS_ROOM = "/v1/room/change-status/{id}/{status}";
export const FUNCTIONAL_ROOM_TYPE =
  "/v1/master-data/functional-classroom-type/list";

// Teacher
export const TEACHER_COMBO = "/v1/teacher/combo";
export const TEACHER = "/v1/teacher";
export const TEACHER_GROUP = "/v1/teacher/teacher-group-subject";

// admin account
export const ADMIN_ACCOUNT = "/v1/account/admin";
export const RESET_PASSWORD_ADMIN_ACCOUNT =
  "/v1/account/root-reset-org-password/{id}";

// Domain
export const DOMAIN_CONFIG = "/v1/domain-config";
export const STATUS_DOMAIN = "/v1/domain-config/change-status/{id}/{status}";
export const SHARE_DOMAIN =
  "/v1/domain-config/change-share-domain/{id}/{isShareDomain}";

// Education device
export const EDU_DEVICE = "/v1/device-definition";
export const EDU_DEVICE_DETAIL = "/v1/device-definition/{id}";
export const STATUS_EDU_DEVICE =
  "/v1/device-definition/change-status/{id}/{status}";

// Device list
export const DEVICE_LIST = "/v1/device";

// Application
export const APPLICATION = "/v1/application";
export const STATUS_APPLICATION = "/v1/application/change-status/{id}/{status}";

// Const
export const CONFIG = "/v1/system/const";

// Feature management
export const FEATURE = "/v1/application-feature";

// School Config
export const STATUS_SCHOOL = "/v1/school/change-status/{id}/{status}";
export const GET_MOET_SCHOOLS = "/v1/sync-moet-data/schools";
export const SCHOOL_CONFIG = "/v1/school";
export const ASYNC_MULTI_SCHOOL = "/v1/school/insert-multiple";
export const PUT_SCHOOL_CONFIG = "/v1/school/{id}";

// Account
export const ACCOUNT = "/v1/account";
export const STATUS_ACCOUNT = "/v1/account/change-status/{id}/{status}";
export const UPDATE_ACCOUNT = "/v1/account/update-base-info";
export const RESET_PASSWORD_ACCOUNT = "/v1/account/reset-password/{id}";

// Application function
export const APPLICATION_FUNCTION = "/v1/application-function";
export const STATUS_APPLICATION_FUNCTION =
  "/v1/application-function/change-status/{id}/{status}";

// Permission
export const APPLICATION_PERMISSION = "/v1/application-function";
export const APPLICATION_FEATURE =
  "/v1/application-feature/get-list-application-feature-by-functionId/{functionId}";
export const APPLICATION_FEATURE_FUNCTION =
  "/v1/application-feature/all-for-org";
export const UPDATE_APPLICATION_FEATURE =
  "/v1/application-feature/grant-features-to-function";

// Tăng thiết bị
export const DOCUMENT_ENTRY = "/v1/device-transaction";
export const DOCUMENT_ENTRY_BY_ID = "/v1/device-transaction/{id}";
export const VALID_DOCUMENT_ENTRY = "/v1/device-definition/validate";

// Giảm thiết bị
export const REDUCE_DEVICE = "/v1/reduce-device";
export const PUT_REDUCE_DEVICE = "/v1/reduce-device/{id}";

//School year config
export const SCHOOL_YEAR_CONFIG = "/v1/school-year-config";

// Thanh lý thiết bị
export const DEVICE_LIQUIDATION = "/v1/export-device";
export const DEVICE_LIQUIDATION_BY_ID = "/v1/export-device/{id}";

//Device issue
export const DEVICE_ISSUE = "/v1/device-issue";
export const DEVICE_ISSUE_FIX = "/v1/device-issue/fix";

// Kiểm kê thiết bị
export const INVENTORY_TRANSACTION = "/v1/inventory-transaction";

// Danh sách thiết bị cho kiểm kê
export const INVENTORY_DEVICE_LIST = "/v1/device/find-for-inventory";

// Tuần học
export const SCHOOL_WEEK = "/v1/school-week-config";
export const CONFIG_SCHOOL_WEEK =
  "/v1/school-week-config/create-multiple-from-date";
export const DELETE_MULTIPLE_SCHOOL_WEEK =
  "/v1/school-week-config/delete-multiple";

// mượn
export const BORROW_REQUEST = "/v1/borrow-request";
export const BORROW_REQUEST_UPDATE = "/v1/borrow-request/{id}";
export const BORROW_DEVICE = "/v1/borrow-request/borrow-request-device";
export const BORROW_DEVICE_APPROVE = "/v1/borrow-request/approve-borrow-device";
export const FIND_BORROW_REQUEST_BY_TEACHER =
  "/v1/borrow-request/find-by-teacher-and-week/{teacherId}/{schoolWeekConfigId}";
export const RETURN_DEVICE_LIST = "/v1/borrow-request/borrowing-device";
export const RETURN_DEVICE_HISTORY = "/v1/borrow-request/returned-device";
export const RETURN_DEVICE = "/v1/borrow-request/return-device";
export const CANCEL_BORROW_DEVICE =
  "/v1/borrow-request/cancel-borrowing-device";
export const CANCEL_RETURN_DEVICE = "/v1/borrow-request/cancel-returned-device";

// Danh sách thiết bị
export const DEVICES = "/v1/device/find-all";
export const TRANSFER_DEVICE = "/v1/device-transfer";
export const HISTORY_DEVICE = "/v1/device/history";
export const DELETE_TRANSFER_DEVICE = "/v1/device-transfer/delete-multiple";

export const GET_PERIOD = "/v1/master-data/period-schedule/list";

// Mượn kho phòng
export const BORROW_ROOM = "/v1/borrow-request/borrow-request-room";

// Thống kê
export const DECREASE_DEVICE = "/api/statistic-report/device-decrement";
