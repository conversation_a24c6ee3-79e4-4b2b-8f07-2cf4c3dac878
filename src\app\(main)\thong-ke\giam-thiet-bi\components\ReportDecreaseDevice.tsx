"use client";

import { IReportDecreaseDevice } from "@/app/(main)/thong-ke/giam-thiet-bi/type";
import { TablePageLayout } from "@/components/common";
import {
  ActionType,
  FilterConfig,
} from "@/components/common/TablePageLayout/type";
import { DECREASE_DEVICE, DEVICE_TYPE, ROOM } from "@/constant/api.const";
import { formatDayjsWithType, formatNumber } from "@/utils/format.utils";
import { ColumnDef } from "@tanstack/react-table";
import React from "react";
import FilterCustom from "./FilterCustom";

const ReportDecreaseDevice = () => {
  return (
    <TablePageLayout<IReportDecreaseDevice>
      excelConfig={{
        tableName: "Báo cáo giảm thiết bị",
        fileName: "bao_cao_giam_thiet_bi.xlsx",
      }}
      formatData={formatData}
      methodFetch="POST"
      apiUrl={DECREASE_DEVICE}
      filterConfig={FILTER_CONFIG}
      tableProps={{
        columns: COLUMNS,
      }}
      visibleCol={VISIBLE_COL}
      actions={ACTIONS}
      filterCustom={(props) => <FilterCustom props={props} />}
    />
  );
};

export default ReportDecreaseDevice;

const formatData = (data: IReportDecreaseDevice[]) => {
  return data.map((item) => ({
    ...item,
    documentDate: formatDayjsWithType(item.documentDate),
    totalBroken: formatNumber(item.totalBroken),
    totalLost: formatNumber(item.totalLost),
    totalAvailable: formatNumber(item.totalAvailable),
    totalQuantity: formatNumber(
      item.totalAvailable + item.totalBroken + item.totalLost
    ),
  }));
};

const ACTIONS: ActionType[] = ["exportExcel"];

const FILTER_CONFIG: FilterConfig[] = [
  {
    key: "fromDate",
  },
  {
    key: "toDate",
  },
  {
    label: "Kho phòng",
    key: "roomIds",
    type: "select",
    apiListUrl: ROOM,
    isMulti: true,
    hasAllOption: true,
    isCollapse: true,
  },
  {
    label: "Loại thiết bị",
    key: "schoolDeviceTypeIds",
    type: "select",
    apiListUrl: DEVICE_TYPE,
    isMulti: true,
    hasAllOption: true,
    isCollapse: true,
  },
];

const COLUMNS: ColumnDef<IReportDecreaseDevice>[] = [
  {
    id: "documentDate",
    header: "Ngày giảm",
    size: 100,
    accessorKey: "documentDate",
  },
  {
    id: "deviceName",
    header: "Tên thiết bị",
    accessorKey: "deviceName",
    size: 150,
  },
  {
    id: "schoolDeviceTypeName",
    header: "Loại thiết bị",
    accessorKey: "schoolDeviceTypeName",
    size: 100,
  },
  {
    id: "roomName",
    header: "Kho phòng",
    accessorKey: "roomName",
    size: 100,
  },
  {
    id: "decreaseQuantity",
    header: "Số lượng giảm theo",
    columns: [
      {
        id: "totalBroken",
        header: "Hỏng",
        size: 80,
        accessorKey: "totalBroken",
        meta: {
          align: "right",
        },
      },
      {
        id: "totalLost",
        header: "Mất",
        size: 80,
        accessorKey: "totalLost",
        meta: {
          align: "right",
        },
      },
      {
        id: "totalAvailable",
        header: "Còn SD",
        size: 80,
        accessorKey: "totalAvailable",
        meta: {
          align: "right",
        },
      },
    ],
  },
  {
    id: "totalQuantity",
    header: "Tổng giảm",
    accessorKey: "totalQuantity",
    size: 80,
    meta: {
      align: "right",
    },
  },
];

const VISIBLE_COL = [
  { id: "documentDate", name: "Ngày giảm" },
  { id: "deviceName", name: "Tên thiết bị" },
  { id: "schoolDeviceTypeName", name: "Loại thiết bị" },
  { id: "roomName", name: "Kho/Phòng" },
  { id: "totalBroken", name: "Số lượng hỏng" },
  { id: "totalLost", name: "Số lượng mất" },
  { id: "totalAvailable", name: "Số lượng còn SD" },
  { id: "totalQuantity", name: "Tổng giảm" },
];
