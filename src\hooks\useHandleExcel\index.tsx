import ExcelJS from "exceljs";
import { saveAs } from "file-saver";
import {
  applyBordersToRow,
  applyRowStyles,
  countExcelLeafColumns,
  createHeader,
  createTemplateFileExcel,
  getManagementOrganization,
  getMaxDepth,
  getMaxRowSpan,
  handleLoadExcel,
  handleLoadExcelProps,
  mapRowData,
  PAGE_SETUP_DEFAULT,
} from "./helper";
import { useAppSelector } from "@/redux/hook";
import { selectSchoolInfo } from "@/redux/app.slice";
import { DataConstant } from "@/constant";
import { toast } from "sonner";
import { useCallback, useMemo } from "react";
import { ExcelColumnProps, ExportExcelProps } from "@/models/types";

const useHandleExcel = () => {
  const schoolInfo = useAppSelector(selectSchoolInfo);

  const donViName = useMemo(
    () => getManagementOrganization(schoolInfo),
    [schoolInfo]
  );

  const handleExportExcel = useCallback(
    ({
      sheetId = "sheet-id",
      tableName = "Bảng",
      fileName = "file.xlsx",
      options,
      columns = [],
      rows = [[]],
      rowHeight,
      hasTemplate = true,
      onCustomWorkSheet,
      onCoverSheet,
      configStartColumn,
      notAddEmptyDataRow = false,
      rowHeightHeader,
      hasIndexCol = true,
    }: ExportExcelProps) => {
      const workbook = new ExcelJS.Workbook();
      if (onCoverSheet) {
        onCoverSheet(workbook);
      }

      const worksheet = workbook.addWorksheet(sheetId, {
        pageSetup: PAGE_SETUP_DEFAULT,
        ...options,
      });

      // Xử lý header

      const maxDepth = getMaxDepth(columns);

      const finalColumns: ExcelColumnProps[] = hasIndexCol
        ? [
            {
              name: "STT",
              key: "index",
              width: 6.5,
              rowSpan: maxDepth,
              colSpan: 1,
              style: {
                alignment: { horizontal: "center" },
              },
            },
            ...columns,
          ]
        : columns;

      worksheet.columns = finalColumns.map((item) => ({
        width: item.width,
        key: item.key,
      }));
      const startColumn = configStartColumn || (hasTemplate ? 7 : 1);
      createHeader({
        worksheet,
        columns: finalColumns,
        startRow: startColumn,
        startCol: 1,
        rowHeight: rowHeightHeader,
      });

      // Thêm style header
      applyBordersToRow(worksheet, startColumn, finalColumns.length);

      if (rows.length) {
        rows.forEach((item, index) => {
          const rowData = mapRowData(item, finalColumns, index);
          worksheet.addRow(rowData);
        });

        const rowSpanHeader = getMaxRowSpan(finalColumns) + startColumn;

        applyRowStyles(worksheet, rowSpanHeader, finalColumns, rowHeight);
      } else {
        if (!notAddEmptyDataRow) {
          worksheet.addRow(["Không có dữ liệu!"]);
          worksheet.mergeCells(
            worksheet.rowCount,
            1,
            worksheet.rowCount,
            finalColumns.length
          );
          worksheet.getRow(worksheet.rowCount).alignment = {
            horizontal: "center",
          };
        }
      }

      if (hasTemplate) {
        const totalColDepth = countExcelLeafColumns(finalColumns);

        // Tạo template form
        createTemplateFileExcel({
          worksheet,
          donViName,
          schoolName: schoolInfo?.name,
          tableName,
          totalColumn: totalColDepth,
          isSchool:
            schoolInfo?.groupUnitCode === DataConstant.DON_VI_TYPE.truong,
        });
      }

      if (onCustomWorkSheet) {
        onCustomWorkSheet(worksheet, workbook);
      }

      workbook.xlsx
        .writeBuffer()
        .then((buffer) => {
          saveAs(new Blob([buffer]), fileName);
        })
        .catch((err) => {
          console.log(err);
          toast.error("Thất bại", { description: "Xuất file excel thất bại!" });
        });
    },
    [schoolInfo]
  );

  const handleReadExcel = useCallback(
    async ({
      file,
      objectMapping,
      metaId,
      metaValue,
    }: handleLoadExcelProps) => {
      try {
        const data = await handleLoadExcel({
          file,
          objectMapping,
          metaId,
          metaValue,
        });

        return data;
      } catch (error: any) {
        toast.error("Thất bại", {
          description: error.message || "Đã có lỗi xảy ra!",
        });

        return null;
      }
    },
    []
  );

  return { handleExportExcel, handleReadExcel };
};

export default useHandleExcel;
