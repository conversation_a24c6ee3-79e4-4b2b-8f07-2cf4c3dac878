import { AppImage, AppLink } from "@/components/common";
import { AppLinkProps } from "@/components/common/AppLink";
import { ImageConstant, PathConstant } from "@/constant";
import { useAppSelector } from "@/redux/hook";
import { Box, Typography } from "@mui/material";
import React, { memo } from "react";

const Logo = ({ ...otherProps }: LogoProps) => {
  const isCollapse = useAppSelector((state) => state.appReducer.isCollapse);

  return (
    <Box
      sx={{
        py: "11px",
        px: "11px",
        width: "100%",
      }}
    >
      <AppLink href={PathConstant.ROOT} {...otherProps}>
        {/* <AppImage
          priority="true"
          src={ImageConstant.Logo}
          alt="logo"
          width={194}
          height={48}
          boxProps={{
            margin: "auto",
          }}
          loading="eager"
        /> */}
        <Typography
          // width={194}
          // height={48}
          variant="h4"
          sx={{
            letterSpacing: "1px",
            color: "common.white",
            textAlign: "center",
            whiteSpace: "nowrap",
          }}
        >
          {isCollapse ? "DMS" : "Quản lý thiết bị"}
        </Typography>
      </AppLink>
    </Box>
  );
};

type LogoProps = Omit<AppLinkProps, "href" | "children">;

export default memo(Logo);
