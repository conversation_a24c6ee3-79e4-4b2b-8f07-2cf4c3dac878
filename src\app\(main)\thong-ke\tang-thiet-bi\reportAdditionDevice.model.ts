export interface IDeviceAdditionReport {
  id: number;

  /** Id thiết bị*/
  deviceDefinitionId: number;

  /** Id phòng*/
  roomId?: number;

  /** Tên phòng*/
  roomName?: string;

  /** Số lượng*/
  quantity: number;

  /** Gi<PERSON> tiền*/
  price: number;

  /** Tổng giá tiền (computed property ở phía C#, phía client cần tính lại nếu cần)*/
  totalPrices?: number;

  /** ===== Thông tin thiết bị =====*/

  /** Mã thống kê*/
  statisticCode: string;

  /** Mã thiết bị*/
  deviceCode: string;

  /** Tên thiết bị*/
  deviceName: string;

  /** Đơn vị tính*/
  deviceUnitId: number;

  /** Tên đơn vị tính*/
  deviceUnitName?: string;

  /** Lo<PERSON>i thiết bị*/
  schoolDeviceTypeId?: number;

  /** Tên loại thiết bị*/
  schoolDeviceTypeName?: string;

  /** Loại thiết bị theo DTI*/
  deviceDTITypeId?: number;

  /** Tên loại thiết bị theo DTI*/
  deviceDTITypeName?: string;

  /** Môn học*/
  schoolSubjectId?: number;

  /** Tên môn học*/
  schoolSubjectName?: string;

  /** Danh sách khối*/
  gradeCodes: number[];

  /** Khối lớp (1 khối được chọn)*/
  gradeCode?: string;

  /** Tên khối lớp*/
  gradeName?: string;

  /** Đối tượng sử dụng*/
  userType?: string;

  /** Danh sách đối tượng sử dụng (1 => Giáo viên, 2 => Học sinh)*/
  userTypes: number[];

  /** Nguồn cấp*/
  schoolBudgetCategoryId: number;

  /** Tên nguồn cấp*/
  schoolBudgetCategoryName?: string;
}
