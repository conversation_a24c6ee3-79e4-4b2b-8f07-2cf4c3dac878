:root {
  --border-color: #e0e0e0;
  --primary-color: #459d7aff;
  --background-color: rgba(22, 173, 90, 0.31);
}

.tox-promotion {
  display: none !important;
}
.tox-fullscreen .tox.tox-tinymce-aux,
.tox-fullscreen ~ .tox.tox-tinymce-aux {
  z-index: 1400 !important;
}

.tox-statusbar__help-text {
  display: none !important;
}

.tox.tox-tinymce {
  min-height: 250px;
  font-family: inherit !important;
  border-radius: 4px;
  border: 1px solid;
  border-color: var(--border-color);
}

.tox-mbtn__select-label {
  white-space: nowrap !important;
}

.tox .tox-tbtn {
  cursor: pointer;
}

.tox .tox-tbtn--active,
.tox .tox-tbtn--enabled,
.tox .tox-tbtn--enabled:focus,
.tox .tox-tbtn--enabled:hover,
.tox .tox-tbtn:active,
.tox
  .tox-collection--toolbar
  .tox-collection__item--enabled:not(.tox-collection__item--state-disabled),
.tox-collection__item.tox-collection__item--active {
  background-color: var(--background-color) !important;
  color: black !important;
}

.tox .tox-tbtn--enabled:focus::after,
.tox .tox-split-button:focus::after,
.tox
  .tox-collection--toolbar
  .tox-collection__item--active:not(
    .tox-collection__item--state-disabled
  ):focus::after,
.tox .tox-tbtn:focus::after {
  box-shadow: 0 0 0 2px var(--primary-color) !important;
}

.tox .tox-edit-area::before {
  border-color: var(--primary-color) !important;
}

.tox .tox-dialog__body-nav-item--active {
  border-color: var(--primary-color);
  color: var(--primary-color);
}

.tox .tox-custom-editor:focus-within,
.tox .tox-focusable-wrapper:focus,
.tox .tox-listboxfield .tox-listbox--select:focus,
.tox .tox-textarea-wrap:focus-within,
.tox .tox-textarea:focus,
.tox .tox-textfield:focus,
.tox .tox-toolbar-textfield:focus {
  border-color: var(--primary-color) !important;
  box-shadow: 0 0 0 1px var(--primary-color) !important;
}

.tox .tox-button::before {
  box-shadow: inset 0 0 0 1px #fff, 0 0 0 2px var(--primary-color);
}

.tox .tox-button {
  background-color: var(--primary-color) !important;
  border-color: var(--primary-color) !important;
}

.tox .tox-button--secondary {
  background-color: #f0f0f0 !important;
  border-color: #f0f0f0 !important;
}

.tox .tox-button--naked {
  background-color: transparent !important;
  border-color: transparent !important;
}

.tox .tox-mbtn:focus:not(:disabled)::after {
  box-shadow: 0 0 0 2px var(--primary-color);
}

.tox .tox-mbtn--active,
.tox .tox-mbtn:not(:disabled).tox-mbtn--active:focus {
  background-color: var(--background-color) !important;
}

.tox:not([dir="rtl"]) .tox-toolbar__group:not(:last-of-type) {
  border-color: var(--border-color);
}

.tox .tox-insert-table-picker .tox-insert-table-picker__selected {
  background-color: var(--primary-color);
}

.tox .tox-button::before {
  box-shadow: inset 0 0 0 1px #fff, 0 0 0 2px var(--primary-color) !important;
}
