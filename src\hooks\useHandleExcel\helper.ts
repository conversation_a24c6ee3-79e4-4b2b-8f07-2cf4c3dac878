import { ISchool } from "@/models/app.model";
import { ExcelColumnProps } from "@/models/types";
import { formatNumber, formattedSchoolName } from "@/utils/format.utils";
import ExcelJS, { PageSetup } from "exceljs";

/** <PERSON>àm để kết hợp style mặc định với style tùy chỉnh */
export const getStyleWithDefault = (defaultStyle, customStyle) => {
  // Luôn đảm bảo tiêu đề table header được căn giữa
  const mergedAlignment = {
    ...defaultStyle.alignment,
    ...customStyle.alignment,
    horizontal: "center",
  };

  return {
    ...defaultStyle,
    ...customStyle,
    alignment: mergedAlignment,
  };
};

export const PAGE_SETUP_DEFAULT: Partial<PageSetup> = {
  paperSize: 9, // A4 paper
  orientation: "portrait", // Chế độ chân dung
  fitToPage: true,
  fitToWidth: 1, // Chỉ vừa 1 chiều ngang trang
  fitToHeight: 0,
  margins: {
    left: 0.7,
    right: 0.7,
    top: 0.75,
    bottom: 0.75,
    header: 0.3,
    footer: 0.3,
  },
};

export const DEFAULT_ROW_STYLE = {
  alignment: {
    wrapText: true,
    vertical: "middle" as const,
  },
  font: {
    name: "Times New Roman",
    size: 11,
  },
  border: {
    top: {
      style: "thin" as const,
    },
    left: {
      style: "thin" as const,
    },
    right: {
      style: "thin" as const,
    },
    bottom: {
      style: "thin" as const,
    },
  },
};

export const DEFAULT_HEADER_STYLE = {
  alignment: {
    vertical: "middle" as const,
  },
  font: {
    bold: true,
    name: "Times New Roman",
    size: 11,
  },
  border: {
    top: {
      style: "thin" as const,
    },
    left: {
      style: "thin" as const,
    },
    right: {
      style: "thin" as const,
    },
    bottom: {
      style: "thin" as const,
    },
  },
};

/** Hàm để tạo header */
export const createHeader = ({
  worksheet,
  columns,
  startRow,
  startCol = 1,
  rowHeight = 25,
  mergedRanges = new Set(),
  seenRows = new Set(),
}: {
  worksheet: ExcelJS.Worksheet;
  columns: any[];
  startRow: number;
  startCol?: number;
  rowHeight?: number;
  mergedRanges?: Set<string>;
  seenRows?: Set<number>;
}) => {
  const currentRow = startRow;
  let currentCol = startCol;

  columns.forEach((column) => {
    if (!seenRows.has(currentRow)) {
      worksheet.getRow(currentRow).height = rowHeight;
      seenRows.add(currentRow);
    }

    const cell = worksheet.getCell(currentRow, currentCol);
    cell.value = column.name;
    cell.style = getStyleWithDefault(DEFAULT_HEADER_STYLE, column.style || {});

    if (column.width) {
      worksheet.getColumn(currentCol).width = column.width;
    }

    if (column.rowSpan && column.rowSpan > 1) {
      const endRow = currentRow + column.rowSpan - 1;
      const mergeKey = `${currentRow}-${currentCol}-${endRow}-${currentCol}`;
      if (!mergedRanges.has(mergeKey)) {
        worksheet.mergeCells(currentRow, currentCol, endRow, currentCol);
        mergedRanges.add(mergeKey);
      }

      for (let r = currentRow; r <= endRow; r++) {
        if (!seenRows.has(r)) {
          worksheet.getRow(r).height = rowHeight;
          seenRows.add(r);
        }
      }
    }

    if (column.colSpan && column.colSpan > 1) {
      const endCol = currentCol + column.colSpan - 1;
      const mergeKey = `${currentRow}-${currentCol}-${currentRow}-${endCol}`;
      if (!mergedRanges.has(mergeKey)) {
        worksheet.mergeCells(currentRow, currentCol, currentRow, endCol);
        mergedRanges.add(mergeKey);
      }

      if (column.child) {
        createHeader({
          worksheet,
          columns: column.child as any[],
          startRow: currentRow + 1,
          startCol: currentCol,
          rowHeight,
          mergedRanges,
          seenRows,
        });
      }

      currentCol = endCol + 1;
    } else {
      if (column.child) {
        createHeader({
          worksheet,
          columns: column.child,
          startRow: currentRow + 1,
          startCol: currentCol,
          rowHeight,
          mergedRanges,
          seenRows,
        });
      }
      currentCol++;
    }
  });
};

type createTemplateFileExcelProps = {
  worksheet: ExcelJS.Worksheet;
  donViName?: string;
  schoolName?: string;
  totalColumn: number;
  tableName: string;
  isSchool?: boolean;
};
/**Hàm tạp template excel common */
export const createTemplateFileExcel = ({
  worksheet,
  donViName,
  schoolName,
  totalColumn,
  tableName,
  isSchool,
}: createTemplateFileExcelProps) => {
  worksheet.spliceRows(
    1,
    5,
    [donViName],
    [`${isSchool ? "Trường: " : ""}${formattedSchoolName(schoolName)}`],
    ["Quản lý thiết bị"],
    [],
    [tableName?.toUpperCase()]
  );

  worksheet.mergeCells("A1:G1");
  worksheet.mergeCells("A2:G2");
  worksheet.mergeCells(5, 1, 5, totalColumn);
  worksheet.eachRow((row, rowNumber) => {
    if (rowNumber === 1 || rowNumber === 2 || rowNumber === 3) {
      row.font = {
        bold: true,
        name: "Times New Roman",
      };
    } else if (rowNumber === 5) {
      row.font = {
        size: 14,
        bold: true,
        name: "Times New Roman",
      };
      row.alignment = {
        wrapText: true,
        vertical: "middle",
        horizontal: "center",
      };
      row.height = 40;
    } else if (rowNumber >= 6) {
      return false; // Thoát khỏi vòng lặp khi đến dòng 6
    }
  });
};

/** Hàm apply style header */
export const applyBordersToRow = (worksheet, rowIndex, totalColumns) => {
  const borderStyle = {
    top: { style: "thin" },
    left: { style: "thin" },
    bottom: { style: "thin" },
    right: { style: "thin" },
  };

  for (let col = 1; col <= totalColumns; col++) {
    const cell = worksheet.getCell(rowIndex, col);
    cell.border = borderStyle;
  }
};
export const mapRowData = (item, columns, index?) => {
  const rowData: any[] = [];

  const extractData = (cols) => {
    cols.forEach((col) => {
      if (col.child) {
        extractData(col.child); // Xử lý cột lồng nhau
      } else if (col.key !== undefined) {
        // Nếu là cột STT thì push index + 1, còn lại thì lấy theo key
        if (col.key === "index") {
          rowData.push(index + 1);
        } else {
          const rawValue = item?.[col.key];

          const value =
            (typeof rawValue === "number" && !isNaN(rawValue)
              ? formatNumber(rawValue || "", "")
              : rawValue) ?? "";

          rowData.push(value ?? "");
        }
      }
    });
  };

  extractData(columns);
  return rowData;
};

const getAllColumns = (columns) => {
  let allColumns: any[] = [];

  columns.forEach((col) => {
    if (col.child) {
      allColumns = allColumns.concat(getAllColumns(col.child));
    } else {
      allColumns.push(col);
    }
  });

  return allColumns;
};

/** Hàm apply style cho row excel */
export const applyRowStyles = (worksheet, startRow, columns, heightRow?) => {
  const allColumns = getAllColumns(columns);

  worksheet.eachRow({ includeEmpty: true }, (row, rowIndex) => {
    if (rowIndex >= startRow) {
      if (heightRow) {
        row.height = heightRow;
      }
      row.eachCell({ includeEmpty: true }, (cell, colIndex) => {
        const col = allColumns[colIndex - 1];
        if (col) {
          cell.alignment = {
            horizontal: col.style?.alignment?.horizontal || "left",
            vertical: col.style?.alignment?.vertical || "middle",
            wrapText: col.style?.alignment?.wrapText ?? true,
          };
          cell.font = {
            name: "Times New Roman",
          };
          cell.border = col.style?.border || DEFAULT_ROW_STYLE.border;
        }
      });
    }
  });
};

export const getMaxRowSpan = (columns) => {
  let maxRowSpan = 1;

  columns.forEach((col) => {
    if (col.child) {
      const childMaxRowSpan = getMaxRowSpan(col.child);
      maxRowSpan = Math.max(maxRowSpan, 1 + childMaxRowSpan);
    }
  });

  return maxRowSpan;
};

export type handleLoadExcelProps = {
  file: File;
  objectMapping: Array<{
    key: string;
    valueIndex: number;
  }>;
  metaId?: string;
  metaValue?: string;
};

export const handleLoadExcel = async ({
  file,
  objectMapping,
  metaId,
  metaValue,
}: handleLoadExcelProps): Promise<Array<any>> => {
  const wb = new ExcelJS.Workbook();
  const reader = new FileReader();
  const list: Array<any> = [];

  return new Promise((resolve, reject) => {
    reader.readAsArrayBuffer(file);
    reader.onload = () => {
      const buffer = reader.result as any;
      wb.xlsx
        .load(buffer)
        .then((workbook) => {
          let isCreatedByExcelJS = false;

          const metadataSheet = wb.getWorksheet(metaId);
          if (
            metadataSheet &&
            metadataSheet.getCell("A1").value === metaValue
          ) {
            isCreatedByExcelJS = true;
          }
          if (!isCreatedByExcelJS && metaId && metaValue) {
            reject({
              message: "File không hợp lệ!",
            });
          }

          workbook.eachSheet((sheet, id) => {
            sheet.eachRow((row, rowIndex) => {
              if (rowIndex > 1 && id === 1) {
                const data = {};
                objectMapping.forEach((item) => {
                  data[item.key] =
                    row.getCell(item.valueIndex).value?.toString() || "";
                });

                list.push(data);
              }
            });
          });

          resolve(list);
        })
        .catch((error) => {
          reject(error);
        });
    };

    reader.onerror = (error) => {
      reject(error);
    };
  });
};

export const getManagementOrganization = (school: ISchool | null) => {
  const { managementOrganization, divisionName, doetName, isSchoolOfDoet } =
    school || {};

  if (managementOrganization) return managementOrganization;

  return isSchoolOfDoet ? doetName || "" : "UBND" + (divisionName || "");
};

/**Hàm lấy độ sâu của header excel */
export const getMaxDepth = (
  columns: ExcelColumnProps[],
  currentDepth = 1
): number => {
  if (!columns || columns.length === 0) return currentDepth;

  return Math.max(
    ...columns.map((col) => {
      if (col.child && col.child.length > 0) {
        return getMaxDepth(col.child, currentDepth + 1);
      }
      return currentDepth;
    })
  );
};

/**Hàm tính số lượng các cột header excel */
export const countExcelLeafColumns = (columns: ExcelColumnProps[]): number => {
  let count = 0;

  for (const col of columns) {
    if (col.child && col.child.length > 0) {
      count += countExcelLeafColumns(col.child);
    } else {
      count += 1;
    }
  }

  return count;
};
