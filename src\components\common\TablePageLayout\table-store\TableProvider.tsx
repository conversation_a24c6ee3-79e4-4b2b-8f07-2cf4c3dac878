"use client";

import React, { useEffect, useMemo, useRef } from "react";
import { createTableStore } from "./storeFactory";
import { TableStoreContext } from "./TableContext";
import { handleGetApiOptions } from "../helper";
import equal from "fast-deep-equal";
import { FilterConfig, TableProviderProps } from "../type";

export function TableProvider<T>({
  children,
  apiUrl,
  methodFetch,
  filterConfig,
  formatData,
  fetchAll = false,
  cleanDataFormFiled,
  onResetFilter,
  excelConfig,
}: TableProviderProps<T>) {
  const currentAbortRef = useRef<(() => void) | undefined>(undefined);
  const hasFetchedOnce = useRef(false);

  const store = useMemo(
    () =>
      createTableStore<T>({
        apiUrl,
        methodFetch,
        cleanDataFormFiled,
        formatData,
        fetchAll,
        excelConfig,
        onResetFilter,
      }),
    [apiUrl, fetchAll]
  );

  useEffect(() => {
    const state = store.getState();

    if (filterConfig?.length) {
      const prevFilterConfig = state.filterConfig ?? [];

      const changedFilters = getChangedFilters(prevFilterConfig, filterConfig);

      if (changedFilters.length > 0) {
        state.setFilterConfig(filterConfig);
        state.setFilter(filterConfig);
        handleGetApiOptions(changedFilters, state.setFilterOptions);
        hasFetchedOnce.current = false; // force refetch
      }
    }

    if (!hasFetchedOnce.current) {
      currentAbortRef.current?.();
      currentAbortRef.current = state.fetchCurrentData() || undefined;
      hasFetchedOnce.current = true;
    }
  }, [filterConfig]);

  useEffect(() => {
    const state = store.getState();

    const unsubscribe = store.subscribe(
      (s: any) => [s.pagination, s.filter],
      ([pagination, filter]: any, [prevPagination, prevFilter]: any) => {
        const isPagChanged = !equal(pagination, prevPagination);
        const isFilterChanged = !equal(filter, prevFilter);

        if (isPagChanged || isFilterChanged) {
          currentAbortRef.current?.();
          currentAbortRef.current = state.fetchCurrentData() || undefined;
        }
      },
      {
        fireImmediately: false,
        equalityFn: () => false,
      }
    );

    return () => {
      unsubscribe();
      currentAbortRef?.current?.();
      store.getState().reset();
    };
  }, []);

  return (
    <TableStoreContext.Provider value={store}>
      {children}
    </TableStoreContext.Provider>
  );
}

export function getChangedFilters(
  prev: FilterConfig[],
  current: FilterConfig[]
): FilterConfig[] {
  const prevMap = new Map(prev.map((item) => [item.key, item]));

  return current.filter((currItem) => {
    const prevItem = prevMap.get(currItem.key);

    // Nếu là item mới => fetch
    if (!prevItem) return true;

    // Nếu toàn bộ object khác => fetch
    if (!equal(currItem, prevItem)) return true;

    return false;
  });
}
