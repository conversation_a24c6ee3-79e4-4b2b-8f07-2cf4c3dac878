import { flexRender } from "@tanstack/react-table";
import { memo } from "react";
import styles from "../table.module.scss";
import AppTableCellHeader from "../../AppTableCellHeader";
import Filter from "./Filter";
import { getCommonPinningStyles } from "../helper";

const HeaderTableCell = ({ header }) => {
  return (
    <AppTableCellHeader
      key={header.id}
      id={`header-${header.column.id}`}
      colSpan={header.colSpan}
      onClick={
        header.column.getCanSort()
          ? header.column.getToggleSortingHandler()
          : undefined
      }
      rowSpan={header.rowSpan}
      align="center" // Table Header sẽ luôn căn giữa
      sx={{
        ...(header.column.columnDef.meta as any)?.headerSx,
        ...(header.column.getCanSort()
          ? {
              cursor: "pointer",
              userSelect: "none",
            }
          : {}),
      }}
      style={{ ...getCommonPinningStyles(header.column) }}
    >
      <div>
        {flexRender(header.column.columnDef.header, header.getContext())}
        {header.column.getCanSort() && (
          <span style={{ marginLeft: 6 }}>
            {{
              asc: "⯅",
              desc: "⯆",
            }[header.column.getIsSorted() as string] ?? null}
          </span>
        )}
      </div>
      <div
        {...{
          onDoubleClick: () => header.column.resetSize(),
          onMouseDown: header.getResizeHandler(),
          onTouchStart: header.getResizeHandler(),
          className: `${styles.resizer} ${
            header.column.getIsResizing() ? styles.isResizing : ""
          }`,
        }}
      />
      {header.column.getCanFilter() &&
      header.column.columnDef?.meta?.filterVariant ? (
        <div>
          <Filter column={header.column} />
        </div>
      ) : null}
    </AppTableCellHeader>
  );
};

export default memo(HeaderTableCell);
