import BorrowHistoryTable from "@/app/(main)/thiet-bi/danh-sach-thiet-bi/components/HistoryDevice/BorrowHistoryTable";
import HistoryDeviceTable from "@/app/(main)/thiet-bi/danh-sach-thiet-bi/components/HistoryDevice/HistoryDeviceTable";
import {
  deviceActions,
  selectorHistoryDevice,
} from "@/app/(main)/thiet-bi/danh-sach-thiet-bi/device.slice";
import { CloseIcon } from "@/components/icons";
import { useAppDispatch, useAppSelector } from "@/redux/hook";
import {
  Drawer,
  IconButton,
  Stack,
  Tab,
  Tabs,
  Typography,
} from "@mui/material";
import React, { useCallback, useState } from "react";

const HistoryDeviceModal = ({
  open,
  onClose,
}: {
  open: boolean;
  onClose: () => void;
}) => {
  const dispatch = useAppDispatch();
  const historyDevice = useAppSelector(selectorHistoryDevice);
  const [tabValue, setTabValue] = useState(HISTORY_DEVICE_TABS.Change);

  const handleCloseHistoryDevice = useCallback(() => {
    dispatch(deviceActions.resetHistoryDevice());
    setTabValue(HISTORY_DEVICE_TABS.Change);
    onClose();
  }, [dispatch, onClose]);

  return (
    <Drawer
      open={open}
      onClose={handleCloseHistoryDevice}
      anchor="right"
      hideBackdrop
      ModalProps={{
        keepMounted: true,
        sx: {
          pointerEvents: "none",
        },
      }}
      slotProps={{
        paper: {
          sx: {
            width: 650,
            pt: 2,
            px: 2,
            pointerEvents: "auto",
          },
        },
      }}
    >
      <Stack direction="row" justifyContent="space-between" alignItems="center">
        <Typography variant="body2" px={2} fontWeight={700}>
          {historyDevice?.deviceName} (ĐVT: {historyDevice?.deviceUnitName})
        </Typography>

        <IconButton onClick={handleCloseHistoryDevice}>
          <CloseIcon />
        </IconButton>
      </Stack>

      <Tabs
        value={tabValue}
        onChange={(_, value) => setTabValue(value)}
        id="history-device-tabs"
        sx={{
          mb: 1,
        }}
      >
        <Tab label="Lịch sử biến động" value={HISTORY_DEVICE_TABS.Change} />
        <Tab label="Tình hình mượn" value={HISTORY_DEVICE_TABS.Borrow} />
      </Tabs>
      {tabValue === HISTORY_DEVICE_TABS.Change && <HistoryDeviceTable />}
      {tabValue === HISTORY_DEVICE_TABS.Borrow && <BorrowHistoryTable />}
    </Drawer>
  );
};

export default HistoryDeviceModal;

export enum HISTORY_DEVICE_TABS {
  Change = 1,
  Borrow,
}
