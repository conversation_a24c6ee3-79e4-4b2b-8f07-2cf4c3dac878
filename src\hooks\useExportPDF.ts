import { useState } from "react";
import { toast } from "sonner";
import { toggleAppProgress } from "@/utils/common.utils";

interface Column {
  header?: string;
  title?: string;
  label?: string;
  field?: string;
  key?: string;
  accessorKey?: string;
}

interface ExportPDFOptions {
  tableData: any[];
  columns: Column[];
  orientation?: "portrait" | "landscape";
  title?: string;
  onSuccess?: () => void;
  onError?: (error: string) => void;
}

const useExportPDF = () => {
  const [isExporting, setIsExporting] = useState(false);

  const exportToPDF = async ({
    tableData,
    columns,
    orientation = "portrait",
    title = "Báo cáo",
    onSuccess,
    onError,
  }: ExportPDFOptions) => {
    if (!tableData || tableData.length === 0) {
      toast.warning("Thất bại!", {
        description: "Không có dữ liệu để xuất",
      });
      return;
    }

    if (!columns || columns.length === 0) {
      toast.warning("Thất bại!", {
        description: "Không có cột nào được định nghĩa",
      });
      return;
    }

    try {
      setIsExporting(true);
      toggleAppProgress(true);

      const response = await fetch("/api/export-pdf", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          tableData,
          columns,
          orientation,
          title,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Có lỗi xảy ra khi tạo PDF");
      }

      // Tạo blob từ response
      const blob = await response.blob();

      // Tạo URL để download
      const url = window.URL.createObjectURL(blob);

      // Tạo link để download
      const link = document.createElement("a");
      link.href = url;
      link.download = `${title.replace(/[^a-zA-Z0-9]/g, "_")}_${
        new Date().toISOString().split("T")[0]
      }.pdf`;

      // Trigger download
      document.body.appendChild(link);
      link.click();

      // Cleanup
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      toast.success("Thành công!", {
        description: "Xuất PDF thành công",
      });

      onSuccess?.();
    } catch (error: any) {
      console.error("Lỗi khi xuất PDF:", error);

      const errorMessage = error.message || "Có lỗi xảy ra khi xuất PDF";

      toast.error("Thất bại!", {
        description: errorMessage,
      });

      onError?.(errorMessage);
    } finally {
      setIsExporting(false);
      toggleAppProgress(false);
    }
  };

  return {
    exportToPDF,
    isExporting,
  };
};

export default useExportPDF;
