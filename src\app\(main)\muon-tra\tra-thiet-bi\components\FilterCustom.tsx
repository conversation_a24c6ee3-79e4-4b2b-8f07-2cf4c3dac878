import { FilterCustomProps } from "@/components/common/TablePageLayout/ContentPage/HeaderFilter";
import { AppConstant } from "@/constant";
import { Grid } from "@mui/material";
import { useCallback, useMemo, memo } from "react";
import { formatDayjsWithType } from "@/utils/format.utils";
import AppDatePicker from "@/components/common/AppDatePicker";
import dayjs from "dayjs";

const FilterCustom = ({ props }: { props: FilterCustomProps }) => {
  const handleFromDateChange = useCallback(
    (value: any) => {
      if (value) {
        props.onChangeFilter("fromDate")(
          formatDayjsWithType(value, AppConstant.DATE_TIME_YYYYescape)
        );
      } else {
        props.onChangeFilter("fromDate")(null);
      }
    },
    [props.onChangeFilter]
  );
  const handleToDateChange = useCallback(
    (value: any) => {
      if (value) {
        props.onChangeFilter("toDate")(
          formatDayjsWithType(value, AppConstant.DATE_TIME_YYYYescape)
        );
      } else {
        props.onChangeFilter("toDate")(null);
      }
    },
    [props.onChangeFilter]
  );

  const fromDate = useMemo(() => {
    const fromDate = props.filter?.find((item) => item.key === "fromDate")
      ?.value as Date;
    return fromDate ? dayjs(fromDate) : null;
  }, [props.filter]);

  const toDate = useMemo(() => {
    const toDate = props.filter?.find((item) => item.key === "toDate")
      ?.value as Date;
    return toDate ? dayjs(toDate) : null;
  }, [props.filter]);

  return (
    <>
      <Grid container size={2.4}>
        <AppDatePicker
          label="Từ ngày"
          value={fromDate}
          onChange={handleFromDateChange}
          maxDate={toDate}
        />
      </Grid>

      <Grid container size={2.4}>
        <AppDatePicker
          label="Đến ngày"
          value={toDate}
          onChange={handleToDateChange}
          minDate={fromDate}
        />
      </Grid>
    </>
  );
};

export default memo(FilterCustom);
