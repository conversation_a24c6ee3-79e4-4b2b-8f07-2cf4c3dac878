import { AppImage } from "@/components/common";
import { ImageConstant } from "@/constant";
import { Box, Stack, Typography } from "@mui/material";
import { memo } from "react";

const LeftSide = () => {
  return (
    <Box
      sx={{
        height: "100%",
        width: 640,
        maxWidth: 640,
        py: 3,
        pl: "60px",
        pr: "40px",
        display: "flex",
        flexDirection: "column",
        justifyContent: "center",
        color: "common.white",
      }}
    >
      <Stack direction="row" alignItems="center" spacing={1.5}>
        <div
          style={{
            width: 60,
            height: 60,
            backgroundColor: "white",
            padding: 9,
            borderRadius: "8px",
          }}
        >
          <AppImage
            alt="logo"
            src={ImageConstant.LogoImage}
            width={42}
            height={42}
            loading="eager"
            fetchPriority="high"
          />
        </div>
        <Typography fontSize={15} fontWeight={800}>
          eNetDevice
        </Typography>
      </Stack>
      <Typography
        fontSize={48}
        lineHeight={"71px"}
        fontWeight={700}
        letterSpacing="-1px"
        marginTop={"67px"}
      >
        <PERSON>ệ thống{" "}
        <Typography
          component="span"
          fontSize={48}
          lineHeight={"71px"}
          fontWeight={700}
          color={"#32C36C"}
        >
          Quản lý thiết bị
        </Typography>{" "}
        trường học
      </Typography>
      <Typography fontSize={17} lineHeight={"36px"} marginTop={2.5}>
        Quản lý thiết bị, cơ sở vật chất trong trường học một cách hiệu quả.
        Theo dõi tình trạng, lịch sử sử dụng, bảo trì và kiểm kê thiết bị định
        kỳ để đảm bảo hoạt động giảng dạy và học tập.
      </Typography>

      <AppImage
        alt="ảnh bìa"
        boxProps={{
          mt: "44px",
        }}
        loading="eager"
        src={ImageConstant.LoginSlide1Image}
        width={363}
        height={325}
        fetchPriority="high"
      />
    </Box>
  );
};

export default memo(LeftSide);
