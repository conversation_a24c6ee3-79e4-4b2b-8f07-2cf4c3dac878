"use client";

import { TablePageLayout } from "@/components/common";
import {
  FilterConfig,
  ActionType,
  ITableRef,
} from "@/components/common/TablePageLayout/type";
import { IReturnDeviceList } from "../returnDevice.model";
import { useMemo, useRef, useState } from "react";
import { ColumnDef } from "@tanstack/react-table";
import { formatDayjsWithType, formatNumber } from "@/utils/format.utils";
import { RETURN_DEVICE_LIST, TEACHER_COMBO } from "@/constant/api.const";
import dynamic from "next/dynamic";
import { Button } from "@mui/material";

import ConfirmModal from "./ConfirmModal";
import FilterCustom from "./FilterCustom";

const ReturnDevice = dynamic(() => import("./ReturnDevice"), {
  ssr: false,
});

const ReturnDeviceList = () => {
  const tableRef = useRef<ITableRef>(null);
  const [selectedRows, setSelectedRows] = useState<IReturnDeviceList[]>([]);
  const [showConfirmModal, setShowConfirmModal] = useState(false);

  const customActions = useMemo(() => {
    return (
      <>
        <ReturnDevice
          selectedRows={selectedRows}
          fetchCurrentData={() => {
            tableRef.current?.fetchCurrentData?.();
          }}
        />
        <Button
          variant="contained"
          color="primary"
          onClick={() => setShowConfirmModal(true)}
          disabled={selectedRows.length === 0}
        >
          Bỏ ghi mượn ({selectedRows.length})
        </Button>
      </>
    );
  }, [selectedRows]);

  const tableProps = useMemo(() => {
    return {
      columns: COLUMNS,
      onRowSelectionChange: (selectedRowsData: IReturnDeviceList[]) => {
        setSelectedRows(selectedRowsData);
      },
    };
  }, []);

  return (
    <>
      <TablePageLayout<IReturnDeviceList>
        ref={tableRef}
        apiUrl={RETURN_DEVICE_LIST}
        methodFetch="POST"
        tableProps={tableProps}
        actions={ACTIONS}
        filterConfig={FILTER_CONFIG}
        customActions={customActions}
        filterCustom={(props) => <FilterCustom props={props} />}
      />

      <ConfirmModal
        open={showConfirmModal}
        onClose={() => setShowConfirmModal(false)}
        onSuccess={() => {
          tableRef.current?.fetchCurrentData?.();
        }}
        type="cancel-borrowing"
        selectedRows={selectedRows}
      />
    </>
  );
};

const ACTIONS: ActionType[] = ["check"];

const FILTER_CONFIG: FilterConfig[] = [
  {
    key: "fromDate",
  },
  {
    key: "toDate",
  },
  {
    key: "teacherIds",
    type: "select",
    label: "Giáo viên",
    isMulti: true,
    apiListUrl: TEACHER_COMBO,
    hasAllOption: true,
    isCollapse: true,
  },
  {
    key: "searchKey",
    type: "text",
    label: "Tìm kiếm",
    isCollapse: true,
  },
];

const COLUMNS: ColumnDef<IReturnDeviceList>[] = [
  {
    id: "fromDate",
    header: "Ngày mượn",
    accessorKey: "fromDate",
    cell: ({ row }) => formatDayjsWithType(row.original.fromDate),
    size: 60,
  },
  {
    id: "teacherName",
    header: "Giáo viên",
    accessorKey: "teacherName",
  },
  {
    id: "deviceCode",
    header: "Mã thiết bị",
    accessorKey: "deviceCode",
  },
  {
    id: "deviceName",
    header: "Tên thiết bị",
    accessorKey: "deviceName",
  },
  {
    id: "deviceUnitName",
    header: "Đơn vị thiết bị",
    accessorKey: "deviceUnitName",
    size: 60,
  },
  {
    id: "quantity",
    header: "SL đang mượn",
    accessorKey: "quantity",
    meta: { align: "right" },
    cell: ({ row }) => formatNumber(row.original.quantity),
    size: 60,
  },
  {
    id: "roomName",
    header: "Kho/Phòng",
    accessorKey: "roomName",
  },
];

export default ReturnDeviceList;
